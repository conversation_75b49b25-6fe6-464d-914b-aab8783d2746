/**
 * Contact Form JavaScript
 * ElshadaiFM Theme - Contact Page
 */

(function($) {
    'use strict';

    // Contact Form Module
    window.ContactForm = {
        
        // Configuration
        config: {
            formSelector: '#contactForm',
            messageSelector: '#formMessage',
            submitButtonSelector: '.submit-btn',
            requiredFields: ['nama', 'email', 'subjek', 'pesan'],
            emailRegex: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
            phoneRegex: /^[\+]?[0-9\-\(\)\s]+$/
        },

        // Initialize form functionality
        init: function() {
            this.$form = $(this.config.formSelector);
            this.$message = $(this.config.messageSelector);
            this.$submitButton = $(this.config.submitButtonSelector);
            
            if (this.$form.length) {
                this.bindEvents();
                this.initValidation();
                console.log('Contact Form initialized');
            }
        },

        // Bind form events
        bindEvents: function() {
            this.$form.on('submit', this.handleSubmit.bind(this));
            
            // Real-time validation
            this.$form.find('input, textarea').on('blur', this.validateField.bind(this));
            this.$form.find('input, textarea').on('input', this.clearFieldError.bind(this));
            
            // Enhanced UX
            this.$form.find('input, textarea').on('focus', this.handleFieldFocus.bind(this));
        },

        // Initialize validation
        initValidation: function() {
            // Add validation attributes
            this.$form.find('input[required], textarea[required]').each(function() {
                $(this).attr('aria-required', 'true');
            });
        },

        // Handle form submission
        handleSubmit: function(e) {
            e.preventDefault();
            
            if (this.validateForm()) {
                this.submitForm();
            }
        },

        // Validate entire form
        validateForm: function() {
            let isValid = true;
            const formData = this.getFormData();
            
            // Clear previous errors
            this.clearAllErrors();
            
            // Validate required fields
            this.config.requiredFields.forEach(fieldName => {
                if (!formData[fieldName] || formData[fieldName].trim() === '') {
                    this.showFieldError(fieldName, 'Field ini wajib diisi');
                    isValid = false;
                }
            });
            
            // Validate email format
            if (formData.email && !this.config.emailRegex.test(formData.email)) {
                this.showFieldError('email', 'Format email tidak valid');
                isValid = false;
            }
            
            // Validate phone format (if provided)
            if (formData.telepon && formData.telepon.trim() !== '' && !this.config.phoneRegex.test(formData.telepon)) {
                this.showFieldError('telepon', 'Format nomor telepon tidak valid');
                isValid = false;
            }
            
            // Validate message length
            if (formData.pesan && formData.pesan.length < 10) {
                this.showFieldError('pesan', 'Pesan minimal 10 karakter');
                isValid = false;
            }
            
            return isValid;
        },

        // Validate individual field
        validateField: function(e) {
            const $field = $(e.target);
            const fieldName = $field.attr('name');
            const value = $field.val().trim();
            
            this.clearFieldError(fieldName);
            
            // Required field validation
            if (this.config.requiredFields.includes(fieldName) && value === '') {
                this.showFieldError(fieldName, 'Field ini wajib diisi');
                return false;
            }
            
            // Email validation
            if (fieldName === 'email' && value !== '' && !this.config.emailRegex.test(value)) {
                this.showFieldError(fieldName, 'Format email tidak valid');
                return false;
            }
            
            // Phone validation
            if (fieldName === 'telepon' && value !== '' && !this.config.phoneRegex.test(value)) {
                this.showFieldError(fieldName, 'Format nomor telepon tidak valid');
                return false;
            }
            
            // Message length validation
            if (fieldName === 'pesan' && value !== '' && value.length < 10) {
                this.showFieldError(fieldName, 'Pesan minimal 10 karakter');
                return false;
            }
            
            return true;
        },

        // Handle field focus
        handleFieldFocus: function(e) {
            const $field = $(e.target);
            const $formGroup = $field.closest('.form-group');
            
            $formGroup.addClass('form-group--focused');
            
            $field.on('blur.focus', function() {
                $formGroup.removeClass('form-group--focused');
                $field.off('blur.focus');
            });
        },

        // Submit form
        submitForm: function() {
            const formData = this.getFormData();
            
            // Show loading state
            this.showLoading();
            
            // Simulate form submission (replace with actual AJAX call)
            setTimeout(() => {
                this.handleSubmitSuccess();
            }, 2000);
            
            // Uncomment and modify for actual AJAX submission:
            /*
            $.ajax({
                url: contactData.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'submit_contact_form',
                    nonce: contactData.nonce,
                    ...formData
                },
                success: this.handleSubmitSuccess.bind(this),
                error: this.handleSubmitError.bind(this)
            });
            */
        },

        // Handle successful submission
        handleSubmitSuccess: function(response) {
            this.hideLoading();
            this.showMessage('Pesan Anda berhasil dikirim! Kami akan segera menghubungi Anda.', 'success');
            this.resetForm();
            
            // Track success event
            if (typeof gtag !== 'undefined') {
                gtag('event', 'form_submit', {
                    event_category: 'Contact',
                    event_label: 'Success'
                });
            }
        },

        // Handle submission error
        handleSubmitError: function(xhr, status, error) {
            this.hideLoading();
            this.showMessage('Terjadi kesalahan saat mengirim pesan. Silakan coba lagi.', 'error');
            
            console.error('Contact form submission error:', error);
            
            // Track error event
            if (typeof gtag !== 'undefined') {
                gtag('event', 'form_error', {
                    event_category: 'Contact',
                    event_label: error
                });
            }
        },

        // Get form data
        getFormData: function() {
            const formData = {};
            
            this.$form.find('input, textarea').each(function() {
                const $field = $(this);
                formData[$field.attr('name')] = $field.val().trim();
            });
            
            return formData;
        },

        // Show field error
        showFieldError: function(fieldName, message) {
            const $field = this.$form.find(`[name="${fieldName}"]`);
            const $formGroup = $field.closest('.form-group');
            
            $formGroup.addClass('form-group--error');
            
            // Remove existing error message
            $formGroup.find('.field-error').remove();
            
            // Add error message
            const $errorMessage = $(`<span class="field-error">${message}</span>`);
            $formGroup.append($errorMessage);
            
            // Add ARIA attributes
            $field.attr('aria-invalid', 'true');
            $field.attr('aria-describedby', `${fieldName}-error`);
            $errorMessage.attr('id', `${fieldName}-error`);
        },

        // Clear field error
        clearFieldError: function(fieldName) {
            if (typeof fieldName === 'object') {
                fieldName = $(fieldName.target).attr('name');
            }
            
            const $field = this.$form.find(`[name="${fieldName}"]`);
            const $formGroup = $field.closest('.form-group');
            
            $formGroup.removeClass('form-group--error');
            $formGroup.find('.field-error').remove();
            
            $field.removeAttr('aria-invalid');
            $field.removeAttr('aria-describedby');
        },

        // Clear all errors
        clearAllErrors: function() {
            this.$form.find('.form-group').removeClass('form-group--error');
            this.$form.find('.field-error').remove();
            this.$form.find('input, textarea').removeAttr('aria-invalid aria-describedby');
        },

        // Show loading state
        showLoading: function() {
            this.$submitButton.addClass('btn--loading').prop('disabled', true);
            this.$submitButton.find('.arrow').text('⏳');
        },

        // Hide loading state
        hideLoading: function() {
            this.$submitButton.removeClass('btn--loading').prop('disabled', false);
            this.$submitButton.find('.arrow').text('→');
        },

        // Show message
        showMessage: function(message, type) {
            this.$message.removeClass('success error').addClass(type);
            this.$message.text(message).show();
            
            // Auto hide after 5 seconds
            setTimeout(() => {
                this.$message.fadeOut();
            }, 5000);
            
            // Scroll to message
            $('html, body').animate({
                scrollTop: this.$message.offset().top - 100
            }, 300);
        },

        // Reset form
        resetForm: function() {
            this.$form[0].reset();
            this.clearAllErrors();
            this.$form.find('.form-group').removeClass('form-group--focused');
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        ContactForm.init();
    });

})(jQuery);
