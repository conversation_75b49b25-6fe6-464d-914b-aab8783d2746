/* Team Section Styles - Matching other sections */
.team-section {
    padding: 60px 0;
    background: #ffffff;
    position: relative;
}

.team-section .about-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.team-section .section-header {
    text-align: center;
    margin-bottom: 50px;
}

.team-section .section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 16px;
    position: relative;
    display: inline-block;
}

.team-section .section-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    border-radius: 2px;
}

.team-section .section-subtitle {
    font-size: 1.125rem;
    color: #64748b;
    max-width: 600px;
    margin: 0 auto;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    max-width: 900px;
    margin: 0 auto;
}

.team-grid .team-member:nth-child(4) {
    justify-self: center;
}

.team-grid .team-member:nth-child(5) {
    justify-self: center;
}

.team-member {
    background: white;
    border-radius: 24px;
    padding: 25px;
    text-align: center;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(226, 232, 240, 0.6);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.team-member:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.member-photo {
    margin-bottom: 15px;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid #e2e8f0;
    transition: border-color 0.3s ease;
    display: block;
    margin: 0 auto;
    position: relative;
}

.member-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center top;
    display: block;
}

.team-member:hover .member-photo {
    border-color: #8B5CF6;
}

.member-info {
    padding: 0 5px;
}

.member-name {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .team-section {
        padding: 50px 0;
    }

    .team-section .section-title {
        font-size: 2rem;
    }

    .team-section .section-subtitle {
        font-size: 1rem;
    }

    .team-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
        max-width: 600px;
    }

    .team-grid .team-member:nth-child(4),
    .team-grid .team-member:nth-child(5) {
        justify-self: center;
    }

    .team-member {
        padding: 25px;
    }

    .member-photo {
        width: 120px;
        height: 120px;
    }

    .member-name {
        font-size: 1.125rem;
    }
}

@media (max-width: 480px) {
    .team-section {
        padding: 40px 0;
    }

    .team-section .section-header {
        margin-bottom: 40px;
    }

    .team-grid {
        grid-template-columns: 1fr;
        gap: 15px;
        max-width: 300px;
    }

    .team-grid .team-member:nth-child(4),
    .team-grid .team-member:nth-child(5) {
        justify-self: center;
    }

    .team-member {
        padding: 20px;
    }

    .member-photo {
        width: 100px;
        height: 100px;
    }

    .member-name {
        font-size: 1rem;
    }
}

/* Accessibility */
.team-member:focus-within {
    outline: 3px solid #8B5CF6;
    outline-offset: 2px;
}

.member-photo img:focus {
    outline: 3px solid #8B5CF6;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .team-member {
        border: 2px solid #1e293b;
    }

    .team-member:hover {
        border-color: #8B5CF6;
    }
}