/* ==========================================================================
   RENUNGAN STYLES
   ========================================================================== */

/* Single Renungan Page */
.renungan-single {
    background: #f8fafc;
    min-height: 100vh;
    padding: 40px 0;
}

.renungan-article {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.renungan-header {
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    color: white;
    padding: 40px;
    text-align: center;
    position: relative;
}

.renungan-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 20px solid transparent;
    border-right: 20px solid transparent;
    border-top: 20px solid #7C3AED;
}

.renungan-meta {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 20px;
    font-size: 14px;
    opacity: 0.9;
}

.renungan-meta span {
    background: rgba(255, 255, 255, 0.2);
    padding: 5px 15px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

.renungan-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 20px 0;
    line-height: 1.2;
    font-family: 'Poppins', sans-serif;
}

.renungan-featured-image {
    margin-top: 30px;
    border-radius: 15px;
    overflow: hidden;
}

.renungan-featured-image img {
    width: 100%;
    height: auto;
    display: block;
}

.renungan-scripture {
    background: linear-gradient(135deg, #C084FC, #A855F7);
    margin: -20px 40px 40px;
    padding: 30px;
    border-radius: 15px;
    color: white;
    text-align: center;
    position: relative;
    z-index: 2;
}

.scripture-icon {
    font-size: 2rem;
    margin-bottom: 15px;
}

.scripture-verse {
    font-size: 1.3rem;
    font-style: italic;
    line-height: 1.6;
    margin: 0 0 15px 0;
    font-weight: 300;
}

.scripture-reference {
    font-size: 1rem;
    font-weight: 600;
    opacity: 0.9;
}

.renungan-content {
    padding: 40px;
    font-size: 1.1rem;
    line-height: 1.8;
    color: #334155;
}

.renungan-content p {
    margin-bottom: 20px;
}

.renungan-prayer {
    background: #f1f5f9;
    margin: 0 40px 40px;
    padding: 30px;
    border-radius: 15px;
    border-left: 4px solid #8B5CF6;
}

.prayer-title {
    color: #8B5CF6;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.prayer-title::before {
    content: '🙏';
    font-size: 1.2rem;
}

.prayer-content {
    color: #475569;
    font-style: italic;
    line-height: 1.7;
}

.renungan-author {
    text-align: center;
    padding: 20px 40px;
    border-top: 1px solid #e2e8f0;
    color: #64748b;
}

.author-label {
    font-size: 14px;
}

.author-name {
    font-weight: 600;
    color: #8B5CF6;
    margin-left: 5px;
}

.renungan-navigation {
    background: #f8fafc;
    padding: 30px 40px;
}

.nav-link {
    display: block;
    padding: 20px;
    background: white;
    border-radius: 10px;
    text-decoration: none;
    color: #334155;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.nav-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(139, 92, 246, 0.2);
    color: #8B5CF6;
}

.nav-direction {
    display: block;
    font-size: 14px;
    color: #8B5CF6;
    font-weight: 600;
    margin-bottom: 5px;
}

.nav-title {
    display: block;
    font-weight: 500;
    line-height: 1.4;
}

.renungan-share {
    padding: 30px 40px;
    background: #f8fafc;
    text-align: center;
}

.renungan-share h4 {
    color: #334155;
    margin-bottom: 20px;
    font-size: 1.1rem;
}

.share-buttons a {
    padding: 10px 20px;
    border-radius: 25px;
    color: white;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.share-facebook {
    background: #1877f2;
}

.share-twitter {
    background: #1da1f2;
}

.share-whatsapp {
    background: #25d366;
}

.share-buttons a:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.related-renungan {
    max-width: 800px;
    margin: 40px auto 0;
    padding: 40px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.related-renungan h3 {
    color: #334155;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 30px;
    text-align: center;
}

.related-posts {
    display: grid;
    gap: 20px;
}

.related-post {
    padding: 20px;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.related-post:hover {
    border-color: #8B5CF6;
    background: #f8fafc;
}

.related-post h4 {
    margin-bottom: 10px;
}

.related-post h4 a {
    color: #334155;
    text-decoration: none;
    font-size: 1.1rem;
}

.related-post h4 a:hover {
    color: #8B5CF6;
}

.related-date {
    color: #64748b;
    font-size: 14px;
    margin-bottom: 10px;
}

.related-excerpt {
    color: #475569;
    line-height: 1.6;
}

/* Archive Renungan Page Specific Styles */
.filter-btn {
    padding: 10px 20px;
    background: white;
    border: 2px solid #e2e8f0;
    border-radius: 25px;
    color: #64748b;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.filter-btn:hover,
.filter-btn.active {
    background: #8B5CF6;
    color: white;
    border-color: #8B5CF6;
}

.search-box input[type="search"] {
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    outline: none;
    min-width: 250px;
}

.search-box button {
    background: #8B5CF6;
    border: none;
    padding: 10px 15px;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    transition: background 0.3s ease;
}

.search-box button:hover {
    background: #7C3AED;
}

.renungan-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.renungan-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 60px rgba(139, 92, 246, 0.2);
}

.card-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.renungan-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.renungan-card:hover .renungan-thumbnail {
    transform: scale(1.1);
}

.card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(139, 92, 246, 0.8), rgba(124, 58, 237, 0.8));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.renungan-card:hover .card-overlay {
    opacity: 1;
}

.read-more {
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
}

.card-content {
    padding: 30px;
}

.card-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-size: 14px;
}

.card-date {
    color: #64748b;
}

.card-category {
    background: #8B5CF6;
    color: white;
    padding: 3px 12px;
    border-radius: 15px;
    font-weight: 500;
}

.card-title {
    margin-bottom: 20px;
}

.card-title a {
    color: #334155;
    text-decoration: none;
    font-size: 1.3rem;
    font-weight: 600;
    line-height: 1.3;
    transition: color 0.3s ease;
}

.card-title a:hover {
    color: #8B5CF6;
}

.card-scripture {
    background: #f1f5f9;
    padding: 15px 20px;
    border-radius: 10px;
    margin-bottom: 15px;
    border-left: 3px solid #8B5CF6;
}

.scripture-text {
    display: block;
    font-style: italic;
    color: #475569;
    line-height: 1.5;
    margin-bottom: 8px;
}

.scripture-ref {
    color: #8B5CF6;
    font-size: 14px;
    font-weight: 600;
}

.card-excerpt {
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 20px;
}

.card-author {
    font-size: 14px;
    color: #64748b;
    margin-bottom: 20px;
}

.author-by {
    margin-right: 5px;
}

.card-actions {
    text-align: center;
}

.read-more-btn {
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    color: white;
    padding: 12px 25px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.read-more-btn:hover {
    background: linear-gradient(135deg, #7C3AED, #6D28D9);
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(139, 92, 246, 0.3);
    color: white;
}

.arrow {
    transition: transform 0.3s ease;
}

.read-more-btn:hover .arrow {
    transform: translateX(5px);
}

.no-posts h3 {
    color: #334155;
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.no-posts p {
    color: #64748b;
    margin-bottom: 30px;
    line-height: 1.6;
}

.renungan-pagination .page-numbers {
    padding: 10px 15px;
    margin: 0 5px;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 5px;
    color: #64748b;
    text-decoration: none;
    transition: all 0.3s ease;
}

.renungan-pagination .page-numbers:hover,
.renungan-pagination .page-numbers.current {
    background: #8B5CF6;
    color: white;
    border-color: #8B5CF6;
}

.renungan-cta h3 {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 15px;
    font-family: 'Poppins', sans-serif;
}

.renungan-cta p {
    font-size: 1.1rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.newsletter-form input[type="email"] {
    flex: 1;
    padding: 15px 20px;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    outline: none;
}

.newsletter-form .btn {
    white-space: nowrap;
}
