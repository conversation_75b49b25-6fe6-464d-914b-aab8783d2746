/* ==========================================================================
   CSS RESET AND BASE STYLES
   ========================================================================== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #ffffff;
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

.logo-image {
    max-width: 150px;
    height: auto;
}

a {
    color: #8B5CF6;
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: #7C3AED;
}

/* ==========================================================================
   UTILITY CLASSES
   ========================================================================== */
.container {
    width: 90%;
    margin: 0 auto;
    padding: 0 20px;
}

.text-center {
    text-align: center;
}

.text-white {
    color: #ffffff !important;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 1rem; }
.mb-2 { margin-bottom: 2rem; }
.mb-3 { margin-bottom: 3rem; }
.mb-4 { margin-bottom: 4rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 1rem; }
.mt-2 { margin-top: 2rem; }
.mt-3 { margin-top: 3rem; }
.mt-4 { margin-top: 4rem; }

.py-1 { padding: 1rem 0; }
.py-2 { padding: 2rem 0; }
.py-3 { padding: 3rem 0; }
.py-4 { padding: 4rem 0; }

/* ==========================================================================
   PURPLE GRADIENT STYLES
   ========================================================================== */
.gradient-bg {
    background: linear-gradient(135deg, #8B5CF6, #7C3AED, #6D28D9);
}

.gradient-bg-light {
    background: linear-gradient(135deg, #C084FC, #A855F7);
}

.gradient-overlay {
    position: relative;
}

.gradient-overlay::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.8), rgba(124, 58, 237, 0.8));
}

/* Screen reader text */
.screen-reader-text {
    clip: rect(1px, 1px, 1px, 1px);
    position: absolute !important;
    height: 1px;
    width: 1px;
    overflow: hidden;
}
