// Contact Page JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const contactForm = document.getElementById('contactForm');
    const formMessage = document.getElementById('formMessage');

    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form data
            const formData = new FormData(contactForm);
            const formDataObj = {};

            for (let [key, value] of formData.entries()) {
                formDataObj[key] = value;
            }

            // Basic validation
            if (!formDataObj.nama || !formDataObj.email || !formDataObj.subjek || !formDataObj.pesan) {
                showMessage('Silakan lengkapi semua field yang wajib diisi.', 'error');
                return;
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(formDataObj.email)) {
                showMessage('Silakan masukkan email yang valid.', 'error');
                return;
            }

            // Add loading state
            contactForm.classList.add('loading');
            const submitBtn = contactForm.querySelector('.submit-btn');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = 'Mengirim...';

            // Simulate form submission (replace with actual AJAX call)
            setTimeout(function() {
                // Here you would normally send the data to your server
                // For demo purposes, we'll just show success message

                contactForm.classList.remove('loading');
                submitBtn.innerHTML = originalText;

                showMessage('Pesan Anda telah berhasil dikirim! Kami akan menghubungi Anda segera.', 'success');
                contactForm.reset();

                // Log the form data (for debugging)
                console.log('Form submitted with data:', formDataObj);
            }, 2000);
        });
    }

    // Show message function
    function showMessage(message, type) {
        if (!formMessage) return;

        formMessage.textContent = message;
        formMessage.className = `form-message ${type}`;
        formMessage.style.display = 'block';

        // Auto-hide after 5 seconds
        setTimeout(() => {
            formMessage.style.display = 'none';
        }, 5000);
    }

    // Phone number formatting
    const phoneInputs = document.querySelectorAll('input[type="tel"]');
    phoneInputs.forEach(input => {
        input.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 0) {
                if (value.length <= 3) {
                    value = value;
                } else if (value.length <= 7) {
                    value = value.slice(0, 3) + '-' + value.slice(3);
                } else {
                    value = value.slice(0, 3) + '-' + value.slice(3, 7) + '-' + value.slice(7, 11);
                }
            }
            e.target.value = value;
        });
    });

    // Smooth scroll for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add animation to contact info items on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe contact info items and FAQ items
    const animateItems = document.querySelectorAll('.contact-info-item, .faq-item');
    animateItems.forEach(item => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
        item.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(item);
    });

    // Copy to clipboard functionality for contact info
    const contactInfoItems = document.querySelectorAll('.contact-info-item');
    contactInfoItems.forEach(item => {
        item.addEventListener('click', function() {
            const textContent = this.querySelector('.info-content p').textContent;
            navigator.clipboard.writeText(textContent).then(function() {
                // Show temporary feedback
                const originalHTML = item.innerHTML;
                item.innerHTML = '<div class="info-icon"><i class="fas fa-check"></i></div><div class="info-content"><h4>Disalin!</h4><p>Info telah disalin ke clipboard</p></div>';

                setTimeout(() => {
                    item.innerHTML = originalHTML;
                }, 2000);
            }).catch(function() {
                console.log('Failed to copy to clipboard');
            });
        });
    });

    // Social media link tracking
    const socialLinks = document.querySelectorAll('.social-link');
    socialLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const platform = this.classList[1]; // Gets the platform class name
            console.log(`Social media click: ${platform}`);

            // Here you would normally send analytics data
            // For example: gtag('event', 'social_click', { platform: platform });
        });
    });

    // Form field focus effects
    const formInputs = document.querySelectorAll('.contact-form input, .contact-form textarea');
    formInputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });

    // FAQ accordion functionality (if you want to make FAQ items collapsible)
    const faqItems = document.querySelectorAll('.faq-item h4');
    faqItems.forEach(item => {
        item.style.cursor = 'pointer';
        item.addEventListener('click', function() {
            const content = this.nextElementSibling;
            const parentItem = this.parentElement;

            if (content.style.display === 'none') {
                content.style.display = 'block';
                parentItem.classList.remove('collapsed');
            } else {
                content.style.display = 'none';
                parentItem.classList.add('collapsed');
            }
        });
    });

    // Initialize map iframe lazy loading
    const mapIframe = document.querySelector('.map-placeholder iframe');
    if (mapIframe) {
        // Set loading="lazy" attribute
        mapIframe.setAttribute('loading', 'lazy');

        // Add error handling
        mapIframe.addEventListener('error', function() {
            this.parentElement.innerHTML = `
                <div class="map-error">
                    <i class="fas fa-map-marker-alt"></i>
                    <p>Peta tidak dapat dimuat</p>
                    <p>Alamat: Jl. Joyonegaran 3A, Kepatihan Kulon, Surakarta, Indonesia</p>
                </div>
            `;
        });
    }
});

// Export functions for potential use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        showMessage: showMessage
    };
}