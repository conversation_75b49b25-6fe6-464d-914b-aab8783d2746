/*
Navigation Styles for ElshadaiFM Theme
======================================
*/

/* Main Navigation Container */
.main-navigation {
    position: relative;
    z-index: 100;
}

/* Mobile Header Layout */
@media (max-width: 768px) {
    .header-nav-section {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        background: rgba(0, 0, 0, 0.9);
        backdrop-filter: blur(10px);
        z-index: 1000;
        padding: 10px 0;
    }
    
    .header-nav-section .container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 20px;
    }
    
    .main-navigation {
        width: 100%;
        display: flex;
        justify-content: flex-end;
        align-items: center;
    }
    
    .header-logo-section {
        position: static;
        top: auto;
        left: auto;
        right: auto;
        z-index: auto;
        backdrop-filter: none;
        padding: 15px 0 8px;
    }
    
    .header-logo-section .site-branding h1 {
        font-size: 1.5rem;
        margin: 0;
        text-align: center;
    }
    
    .header-logo-section .site-tagline {
        font-size: 0.9rem;
        text-align: center;
        margin: 5px 0 0 0;
    }
    
    /* Hero content wrapper - no extra padding needed */
    .hero-content-wrapper {
        padding-top: 0;
    }
}

.main-navigation ul {
    display: flex;
    list-style: none;
    gap: 30px;
    background: rgba(255, 255, 255, 0.1);
    padding: 15px 25px;
    border-radius: 25px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin: 0;
    transition: all 0.3s ease;
}

.main-navigation ul:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
}

/* Navigation Links */
.main-navigation a {
    color: white;
    font-weight: 500;
    padding: 10px 15px;
    position: relative;
    transition: all 0.3s ease;
    border-radius: 15px;
    text-transform: uppercase;
    font-size: 14px;
    letter-spacing: 0.5px;
}

.main-navigation a:hover {
    color: rgba(255, 255, 255, 0.8);
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.main-navigation a::after {
    content: '';
    position: absolute;
    bottom: 5px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 2px;
    background: white;
    transition: width 0.3s ease;
    border-radius: 2px;
}

.main-navigation a:hover::after {
    width: 80%;
}

/* Active/Current Page Styling */
.main-navigation .current-menu-item a,
.main-navigation .current_page_item a {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.main-navigation .current-menu-item a::after,
.main-navigation .current_page_item a::after {
    width: 80%;
}

/* Mobile Navigation Toggle */
.menu-toggle {
    display: none;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.3s ease;
    flex-direction: column;
    justify-content: space-around;
    width: 40px;
    height: 40px;
    position: relative;
}

.menu-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
}

.menu-toggle .menu-icon {
    display: block;
    width: 20px;
    height: 2px;
    background: white;
    transition: all 0.3s ease;
    border-radius: 1px;
    margin: 2px auto;
}

.menu-toggle.active {
    background: rgba(255, 255, 255, 0.2);
}

.menu-toggle.active .menu-icon:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.menu-toggle.active .menu-icon:nth-child(2) {
    opacity: 0;
}

.menu-toggle.active .menu-icon:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* Mobile Dropdown Menu */
@media (max-width: 768px) {
    .main-navigation.mobile-active ul {
        position: fixed;
        top: 60px;
        left: 0;
        right: 0;
        flex-direction: column;
        background: rgba(0, 0, 0, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 0;
        padding: 20px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.5);
        margin-top: 0;
        gap: 0;
    }
    
    .main-navigation.mobile-active a {
        padding: 15px 20px;
        margin: 0;
        border-radius: 0;
        display: block;
        text-align: center;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .main-navigation.mobile-active a:last-child {
        border-bottom: none;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .menu-toggle {
        display: flex;
    }
    
    .main-navigation ul {
        display: none;
    }
    
    .main-navigation.mobile-active ul {
        display: flex;
    }
}

/* Desktop Navigation - hide mobile header styles */
@media (min-width: 769px) {
    .header-nav-section,
    .header-logo-section {
        position: static;
        background: none;
    }
    
    .hero-content-wrapper {
        padding-top: 0;
    }
}

@media (max-width: 480px) {
    .main-navigation ul {
        gap: 15px;
        padding: 12px 20px;
    }
    
    .main-navigation a {
        font-size: 13px;
        padding: 8px 12px;
    }
}

/* Animation Classes for JavaScript */
.nav-fade-in {
    animation: fadeInDown 0.5s ease-out;
}

.nav-fade-out {
    animation: fadeOutUp 0.3s ease-in;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeOutUp {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-20px);
    }
}

/* Scroll Effect Classes */
.nav-scrolled {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: scale(0.95);
}

.nav-scrolled a {
    font-size: 13px;
}