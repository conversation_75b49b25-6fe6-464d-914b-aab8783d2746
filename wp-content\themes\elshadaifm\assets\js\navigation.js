/**
 * Navigation JavaScript for ElshadaiFM Theme
 * ==========================================
 */

(function() {
    'use strict';

    // Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', function() {
        initNavigation();
    });

    function initNavigation() {
        const navigation = document.querySelector('.main-navigation');
        const navToggle = document.querySelector('.menu-toggle');
        
        if (navigation && navToggle) {
            // Initialize mobile menu functionality
            initMobileMenu(navigation, navToggle);
            
            // Initialize scroll effects
            initScrollEffects(navigation);
            
            // Initialize smooth scrolling for anchor links
            initSmoothScrolling();
            
            // Initialize keyboard navigation
            initKeyboardNavigation(navigation);
        }
    }


    /**
     * Initialize mobile menu functionality
     */
    function initMobileMenu(navigation, toggle) {
        toggle.addEventListener('click', function() {
            const isActive = navigation.classList.contains('mobile-active');
            const navList = navigation.querySelector('ul');
            
            if (isActive) {
                // Close menu
                navigation.classList.remove('mobile-active');
                toggle.classList.remove('active');
                toggle.setAttribute('aria-expanded', 'false');
                
                if (navList) {
                    navList.classList.add('nav-fade-out');
                    setTimeout(() => {
                        navList.classList.remove('nav-fade-out');
                    }, 300);
                }
            } else {
                // Open menu
                navigation.classList.add('mobile-active');
                toggle.classList.add('active');
                toggle.setAttribute('aria-expanded', 'true');
                
                if (navList) {
                    navList.classList.add('nav-fade-in');
                    setTimeout(() => {
                        navList.classList.remove('nav-fade-in');
                    }, 500);
                }
            }
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(event) {
            if (!navigation.contains(event.target) && navigation.classList.contains('mobile-active')) {
                navigation.classList.remove('mobile-active');
                toggle.classList.remove('active');
                toggle.setAttribute('aria-expanded', 'false');
            }
        });

        // Close mobile menu on escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape' && navigation.classList.contains('mobile-active')) {
                navigation.classList.remove('mobile-active');
                toggle.classList.remove('active');
                toggle.setAttribute('aria-expanded', 'false');
                toggle.focus();
            }
        });
    }

    /**
     * Initialize scroll effects
     */
    function initScrollEffects(navigation) {
        let scrolled = false;
        
        window.addEventListener('scroll', function() {
            const currentScroll = window.pageYOffset;
            const navList = navigation.querySelector('ul');
            
            if (currentScroll > 100 && !scrolled) {
                scrolled = true;
                if (navList) {
                    navList.classList.add('nav-scrolled');
                }
            } else if (currentScroll <= 100 && scrolled) {
                scrolled = false;
                if (navList) {
                    navList.classList.remove('nav-scrolled');
                }
            }
        });
    }

    /**
     * Initialize smooth scrolling for anchor links
     */
    function initSmoothScrolling() {
        const navLinks = document.querySelectorAll('.main-navigation a[href^="#"]');
        
        navLinks.forEach(function(link) {
            link.addEventListener('click', function(event) {
                const href = this.getAttribute('href');
                const target = document.querySelector(href);
                
                if (target) {
                    event.preventDefault();
                    
                    // Close mobile menu if open
                    const navigation = document.querySelector('.main-navigation');
                    if (navigation && navigation.classList.contains('mobile-active')) {
                        navigation.classList.remove('mobile-active');
                        const toggle = navigation.querySelector('.menu-toggle');
                        if (toggle) {
                            toggle.classList.remove('active');
                            toggle.setAttribute('aria-expanded', 'false');
                        }
                    }
                    
                    // Smooth scroll to target
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                    
                    // Update URL without page jump
                    if (history.pushState) {
                        history.pushState(null, null, href);
                    }
                }
            });
        });
    }

    /**
     * Initialize keyboard navigation
     */
    function initKeyboardNavigation(navigation) {
        const navLinks = navigation.querySelectorAll('a');
        
        navLinks.forEach(function(link, index) {
            link.addEventListener('keydown', function(event) {
                let targetIndex;
                
                switch (event.key) {
                    case 'ArrowRight':
                        event.preventDefault();
                        targetIndex = (index + 1) % navLinks.length;
                        navLinks[targetIndex].focus();
                        break;
                        
                    case 'ArrowLeft':
                        event.preventDefault();
                        targetIndex = (index - 1 + navLinks.length) % navLinks.length;
                        navLinks[targetIndex].focus();
                        break;
                        
                    case 'Home':
                        event.preventDefault();
                        navLinks[0].focus();
                        break;
                        
                    case 'End':
                        event.preventDefault();
                        navLinks[navLinks.length - 1].focus();
                        break;
                }
            });
        });
    }

    /**
     * Add visual feedback for navigation interactions
     */
    function addInteractionFeedback() {
        const navLinks = document.querySelectorAll('.main-navigation a');
        
        navLinks.forEach(function(link) {
            // Add focus visual feedback
            link.addEventListener('focus', function() {
                this.style.outline = '2px solid rgba(255, 255, 255, 0.5)';
                this.style.outlineOffset = '2px';
            });
            
            link.addEventListener('blur', function() {
                this.style.outline = 'none';
            });
            
            // Add click animation
            link.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    }

    // Initialize interaction feedback when DOM is loaded
    document.addEventListener('DOMContentLoaded', addInteractionFeedback);

})();