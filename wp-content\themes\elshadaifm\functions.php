<?php
/**
 * Elshadaifm Theme Functions
 *
 * @package Elshadaifm
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Theme Setup
 */
function elshadaifm_setup() {
    // Add default posts and comments RSS feed links to head
    add_theme_support( 'automatic-feed-links' );

    // Let WordPress manage the document title
    add_theme_support( 'title-tag' );

    // Enable support for Post Thumbnails on posts and pages
    add_theme_support( 'post-thumbnails' );

    // Custom image sizes
    add_image_size( 'elshadaifm-featured', 800, 400, true );
    add_image_size( 'elshadaifm-thumbnail', 300, 200, true );
    add_image_size( 'elshadaifm-team', 350, 350, true );

    // This theme uses wp_nav_menu() in multiple locations
    register_nav_menus( array(
        'primary' => esc_html__( 'Primary Menu', 'elshadaifm' ),
        'footer'  => esc_html__( 'Footer Menu', 'elshadaifm' ),
        'social'  => esc_html__( 'Social Menu', 'elshadaifm' ),
    ) );

    // Switch default core markup for search form, comment form, and comments
    add_theme_support( 'html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ) );

    // Set up the WordPress core custom background feature
    add_theme_support( 'custom-background', apply_filters( 'elshadaifm_custom_background_args', array(
        'default-color' => 'ffffff',
        'default-image' => '',
    ) ) );

    // Add theme support for selective refresh for widgets
    add_theme_support( 'customize-selective-refresh-widgets' );

    // Add support for core custom logo
    add_theme_support( 'custom-logo', array(
        'height'      => 250,
        'width'       => 250,
        'flex-width'  => true,
        'flex-height' => true,
    ) );

    // Add support for wide alignment
    add_theme_support( 'align-wide' );

    // Add support for editor styles
    add_theme_support( 'editor-styles' );
}
add_action( 'after_setup_theme', 'elshadaifm_setup' );

/**
 * Register widget areas
 */
function elshadaifm_widgets_init() {
    register_sidebar( array(
        'name'          => esc_html__( 'Sidebar', 'elshadaifm' ),
        'id'            => 'sidebar-1',
        'description'   => esc_html__( 'Add widgets here.', 'elshadaifm' ),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ) );

    register_sidebar( array(
        'name'          => esc_html__( 'Footer Area 1', 'elshadaifm' ),
        'id'            => 'footer-1',
        'description'   => esc_html__( 'Add widgets here for footer column 1.', 'elshadaifm' ),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ) );

    register_sidebar( array(
        'name'          => esc_html__( 'Footer Area 2', 'elshadaifm' ),
        'id'            => 'footer-2',
        'description'   => esc_html__( 'Add widgets here for footer column 2.', 'elshadaifm' ),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ) );

    register_sidebar( array(
        'name'          => esc_html__( 'Footer Area 3', 'elshadaifm' ),
        'id'            => 'footer-3',
        'description'   => esc_html__( 'Add widgets here for footer column 3.', 'elshadaifm' ),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ) );
}
add_action( 'widgets_init', 'elshadaifm_widgets_init' );

/**
 * Enqueue scripts and styles
 */
function elshadaifm_scripts() {
    // Enqueue main stylesheet
    wp_enqueue_style( 'elshadaifm-style', get_stylesheet_uri(), array(), '1.0' );

    // Enqueue Google Fonts
    wp_enqueue_style( 'elshadaifm-fonts', 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap', array(), '1.0' );

    // Enqueue radio player CSS and JS
    wp_enqueue_style( 'elshadaifm-player', get_template_directory_uri() . '/assets/css/player.css', array(), '1.0' );
    wp_enqueue_script( 'elshadaifm-player', get_template_directory_uri() . '/assets/js/player.js', array(), '1.0', true );

    // Enqueue navigation JavaScript (vanilla JS, no jQuery dependency)
    wp_enqueue_script( 'elshadaifm-navigation', get_template_directory_uri() . '/assets/js/navigation.js', array(), '1.0', true );

    // Custom scripts removed - file not found

    // Enqueue comment reply if needed
    if ( is_singular() && comments_open() && get_option( 'thread_comments' ) ) {
        wp_enqueue_script( 'comment-reply' );
    }

    // Localize script for AJAX
    wp_localize_script( 'elshadaifm-custom', 'elshadaifm_ajax', array(
        'ajax_url' => admin_url( 'admin-ajax.php' ),
        'nonce'    => wp_create_nonce( 'elshadaifm_nonce' )
    ) );
}
add_action( 'wp_enqueue_scripts', 'elshadaifm_scripts' );

/**
 * Custom Post Types
 */
function elshadaifm_custom_post_types() {
    


    // Staff Post Type  
    register_post_type( 'staff', array(
        'labels' => array(
            'name'               => __( 'Staff', 'elshadaifm' ),
            'singular_name'      => __( 'Staff Member', 'elshadaifm' ),
            'menu_name'          => __( 'Staff', 'elshadaifm' ),
            'add_new'            => __( 'Add New Staff', 'elshadaifm' ),
            'add_new_item'       => __( 'Add New Staff Member', 'elshadaifm' ),
            'edit_item'          => __( 'Edit Staff Member', 'elshadaifm' ),
            'new_item'           => __( 'New Staff Member', 'elshadaifm' ),
            'view_item'          => __( 'View Staff Member', 'elshadaifm' ),
            'search_items'       => __( 'Search Staff', 'elshadaifm' ),
            'not_found'          => __( 'No staff found', 'elshadaifm' ),
            'not_found_in_trash' => __( 'No staff found in trash', 'elshadaifm' )
        ),
        'public'      => true,
        'has_archive' => true,
        'menu_icon'   => 'dashicons-groups',
        'supports'    => array( 'title', 'editor', 'thumbnail', 'custom-fields' ),
        'rewrite'     => array( 'slug' => 'staff' ),
    ) );

    // Kesaksian Post Type
    register_post_type( 'kesaksian', array(
        'labels' => array(
            'name'               => __( 'Kesaksian', 'elshadaifm' ),
            'singular_name'      => __( 'Kesaksian', 'elshadaifm' ),
            'menu_name'          => __( 'Kesaksian', 'elshadaifm' ),
            'add_new'            => __( 'Tambah Kesaksian', 'elshadaifm' ),
            'add_new_item'       => __( 'Tambah Kesaksian Baru', 'elshadaifm' ),
            'edit_item'          => __( 'Edit Kesaksian', 'elshadaifm' ),
            'new_item'           => __( 'Kesaksian Baru', 'elshadaifm' ),
            'view_item'          => __( 'Lihat Kesaksian', 'elshadaifm' ),
            'search_items'       => __( 'Cari Kesaksian', 'elshadaifm' ),
            'not_found'          => __( 'Tidak ada kesaksian ditemukan', 'elshadaifm' ),
            'not_found_in_trash' => __( 'Tidak ada kesaksian di trash', 'elshadaifm' )
        ),
        'public'      => true,
        'has_archive' => true,
        'menu_icon'   => 'dashicons-heart',
        'supports'    => array( 'title', 'editor', 'thumbnail', 'custom-fields' ),
        'rewrite'     => array( 'slug' => 'kesaksian' ),
    ) );

    // Renungan Post Type
    register_post_type( 'renungan', array(
        'labels' => array(
            'name'               => __( 'Renungan', 'elshadaifm' ),
            'singular_name'      => __( 'Renungan', 'elshadaifm' ),
            'menu_name'          => __( 'Renungan', 'elshadaifm' ),
            'add_new'            => __( 'Tambah Renungan', 'elshadaifm' ),
            'add_new_item'       => __( 'Tambah Renungan Baru', 'elshadaifm' ),
            'edit_item'          => __( 'Edit Renungan', 'elshadaifm' ),
            'new_item'           => __( 'Renungan Baru', 'elshadaifm' ),
            'view_item'          => __( 'Lihat Renungan', 'elshadaifm' ),
            'search_items'       => __( 'Cari Renungan', 'elshadaifm' ),
            'not_found'          => __( 'Tidak ada renungan ditemukan', 'elshadaifm' ),
            'not_found_in_trash' => __( 'Tidak ada renungan di trash', 'elshadaifm' )
        ),
        'public'      => true,
        'has_archive' => true,
        'menu_icon'   => 'dashicons-book-alt',
        'supports'    => array( 'title', 'editor', 'thumbnail', 'custom-fields' ),
        'rewrite'     => array( 'slug' => 'renungan' ),
    ) );





}
add_action( 'init', 'elshadaifm_custom_post_types' );

/**
 * Custom Meta Boxes
 */
function elshadaifm_add_meta_boxes() {

    // Team member meta box
    add_meta_box(
        'team_details',
        __( 'Team Member Details', 'elshadaifm' ),
        'elshadaifm_team_meta_box',
        'team',
        'normal',
        'high'
    );


  

}
add_action( 'add_meta_boxes', 'elshadaifm_add_meta_boxes' );


/**
 * Team Member Meta Box Callback
 */
function elshadaifm_team_meta_box( $post ) {
    wp_nonce_field( 'elshadaifm_save_team_meta', 'elshadaifm_team_meta_nonce' );
    
    $position = get_post_meta( $post->ID, 'position', true );
    $bio = get_post_meta( $post->ID, 'bio', true );
    $social_links = get_post_meta( $post->ID, 'social_links', true );
    
    echo '<table class="form-table">';
    echo '<tr><th><label for="position">' . __( 'Position/Role', 'elshadaifm' ) . '</label></th>';
    echo '<td><input type="text" id="position" name="position" value="' . esc_attr( $position ) . '" /></td></tr>';
    echo '<tr><th><label for="bio">' . __( 'Short Bio', 'elshadaifm' ) . '</label></th>';
    echo '<td><textarea id="bio" name="bio" rows="4" cols="50">' . esc_textarea( $bio ) . '</textarea></td></tr>';
    echo '<tr><th><label for="social_links">' . __( 'Social Links (JSON format)', 'elshadaifm' ) . '</label></th>';
    echo '<td><textarea id="social_links" name="social_links" rows="3" cols="50">' . esc_textarea( $social_links ) . '</textarea></td></tr>';
    echo '</table>';
}



/**
 * Save Meta Box Data
 */
function elshadaifm_save_meta_boxes( $post_id ) {
    
    // Team meta
    if ( isset( $_POST['elshadaifm_team_meta_nonce'] ) && wp_verify_nonce( $_POST['elshadaifm_team_meta_nonce'], 'elshadaifm_save_team_meta' ) ) {
        update_post_meta( $post_id, 'position', sanitize_text_field( $_POST['position'] ) );
        update_post_meta( $post_id, 'bio', sanitize_textarea_field( $_POST['bio'] ) );
        update_post_meta( $post_id, 'social_links', sanitize_textarea_field( $_POST['social_links'] ) );
    }
    
    
}
add_action( 'save_post', 'elshadaifm_save_meta_boxes' );

/**
 * Custom Excerpt Length
 */
function elshadaifm_excerpt_length( $length ) {
    return 20;
}
add_filter( 'excerpt_length', 'elshadaifm_excerpt_length' );

/**
 * Custom Excerpt More
 */
function elshadaifm_excerpt_more( $more ) {
    return '...';
}
add_filter( 'excerpt_more', 'elshadaifm_excerpt_more' );

/**
 * Add custom body classes
 */
function elshadaifm_body_classes( $classes ) {
    // Add class of group-blog to blogs with more than 1 published author
    if ( is_multi_author() ) {
        $classes[] = 'group-blog';
    }

    // Add class of hfeed to non-singular pages
    if ( ! is_singular() ) {
        $classes[] = 'hfeed';
    }

    return $classes;
}
add_filter( 'body_class', 'elshadaifm_body_classes' );

/**
 * Custom functions for theme
 */

function elshadaifm_get_team_members( $limit = -1 ) {
    return get_posts( array(
        'post_type'      => 'team',
        'posts_per_page' => $limit,
        'post_status'    => 'publish',
        'orderby'        => 'menu_order',
        'order'          => 'ASC'
    ) );
}

// Include testimonials functions
require_once get_template_directory() . '/inc/testimonials-functions.php';

/**
 * Kesaksian Moderation Functions
 */

// Add admin notice for pending kesaksian
function elshadaifm_kesaksian_admin_notice() {
    $pending_count = wp_count_posts('kesaksian')->pending;
    if ($pending_count > 0 && current_user_can('edit_posts')) {
        echo '<div class="notice notice-info is-dismissible">';
        echo '<p><strong>' . sprintf(_n('Ada %d kesaksian menunggu persetujuan.', 'Ada %d kesaksian menunggu persetujuan.', $pending_count, 'elshadaifm'), $pending_count) . '</strong> ';
        echo '<a href="' . admin_url('edit.php?post_type=kesaksian&post_status=pending') . '">Lihat kesaksian pending</a></p>';
        echo '</div>';
    }
}
add_action('admin_notices', 'elshadaifm_kesaksian_admin_notice');

// Add custom columns to kesaksian admin list
function elshadaifm_kesaksian_columns($columns) {
    $new_columns = array();
    $new_columns['cb'] = $columns['cb'];
    $new_columns['title'] = $columns['title'];
    $new_columns['submitter'] = __('Pemberi Kesaksian', 'elshadaifm');
    $new_columns['location'] = __('Lokasi', 'elshadaifm');
    $new_columns['contact'] = __('Kontak', 'elshadaifm');
    $new_columns['submit_date'] = __('Tanggal Submit', 'elshadaifm');
    $new_columns['date'] = $columns['date'];
    
    return $new_columns;
}
add_filter('manage_kesaksian_posts_columns', 'elshadaifm_kesaksian_columns');

// Fill custom columns with data
function elshadaifm_kesaksian_column_content($column, $post_id) {
    switch ($column) {
        case 'submitter':
            $nama = get_post_meta($post_id, 'nama_pemberi_kesaksian', true);
            echo $nama ? esc_html($nama) : '—';
            break;
            
        case 'location':
            $kota = get_post_meta($post_id, 'kota_pemberi_kesaksian', true);
            $negara = get_post_meta($post_id, 'negara_pemberi_kesaksian', true);
            if ($kota || $negara) {
                echo esc_html($kota . ($kota && $negara ? ', ' : '') . $negara);
            } else {
                echo '—';
            }
            break;
            
        case 'contact':
            $email = get_post_meta($post_id, 'email_pemberi_kesaksian', true);
            $phone = get_post_meta($post_id, 'phone_pemberi_kesaksian', true);
            
            $contact_info = array();
            if ($email) {
                $contact_info[] = '<a href="mailto:' . esc_attr($email) . '">' . esc_html($email) . '</a>';
            }
            if ($phone) {
                $contact_info[] = esc_html($phone);
            }
            
            echo !empty($contact_info) ? implode('<br>', $contact_info) : '—';
            break;
            
        case 'submit_date':
            $submit_date = get_post_meta($post_id, 'tanggal_submit', true);
            if ($submit_date) {
                echo date('d M Y H:i', strtotime($submit_date));
            } else {
                echo '—';
            }
            break;
    }
}
add_action('manage_kesaksian_posts_custom_column', 'elshadaifm_kesaksian_column_content', 10, 2);

// Add meta box for kesaksian details
function elshadaifm_add_kesaksian_meta_box() {
    add_meta_box(
        'kesaksian_details',
        __('Detail Kesaksian', 'elshadaifm'),
        'elshadaifm_kesaksian_meta_box_callback',
        'kesaksian',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'elshadaifm_add_kesaksian_meta_box');

// Meta box callback
function elshadaifm_kesaksian_meta_box_callback($post) {
    $nama = get_post_meta($post->ID, 'nama_pemberi_kesaksian', true);
    $kota = get_post_meta($post->ID, 'kota_pemberi_kesaksian', true);
    $negara = get_post_meta($post->ID, 'negara_pemberi_kesaksian', true);
    $email = get_post_meta($post->ID, 'email_pemberi_kesaksian', true);
    $phone = get_post_meta($post->ID, 'phone_pemberi_kesaksian', true);
    $tanggal_submit = get_post_meta($post->ID, 'tanggal_submit', true);
    $ip_address = get_post_meta($post->ID, 'ip_address', true);
    
    echo '<table class="form-table">';
    echo '<tr><th><label>Nama Pemberi Kesaksian:</label></th>';
    echo '<td><strong>' . esc_html($nama) . '</strong></td></tr>';
    
    if ($kota || $negara) {
        echo '<tr><th><label>Lokasi:</label></th>';
        echo '<td>' . esc_html($kota . ($kota && $negara ? ', ' : '') . $negara) . '</td></tr>';
    }
    
    if ($email) {
        echo '<tr><th><label>Email:</label></th>';
        echo '<td><a href="mailto:' . esc_attr($email) . '">' . esc_html($email) . '</a></td></tr>';
    }
    
    if ($phone) {
        echo '<tr><th><label>Nomor Telepon:</label></th>';
        echo '<td><a href="tel:' . esc_attr($phone) . '">' . esc_html($phone) . '</a></td></tr>';
    }
    
    echo '<tr><th><label>Tanggal Submit:</label></th>';
    echo '<td>' . ($tanggal_submit ? date('d M Y H:i:s', strtotime($tanggal_submit)) : '—') . '</td></tr>';
    
    if ($ip_address && current_user_can('manage_options')) {
        echo '<tr><th><label>IP Address:</label></th>';
        echo '<td><code>' . esc_html($ip_address) . '</code></td></tr>';
    }
    echo '</table>';
    
    if ($post->post_status === 'pending') {
        echo '<div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin-top: 20px; border-radius: 4px;">';
        echo '<p><strong>Status:</strong> Kesaksian ini menunggu persetujuan admin.</p>';
        echo '<p>Untuk menerbitkan kesaksian ini, ubah status menjadi "Published" dan klik "Update".</p>';
        echo '</div>';
    }
}

// Email notification for new kesaksian submission
function elshadaifm_notify_admin_new_kesaksian($post_id) {
    $post = get_post($post_id);
    
    if ($post->post_type === 'kesaksian' && $post->post_status === 'pending') {
        $nama = get_post_meta($post_id, 'nama_pemberi_kesaksian', true);
        $email = get_post_meta($post_id, 'email_pemberi_kesaksian', true);
        
        $admin_email = get_option('admin_email');
        $subject = 'Kesaksian Baru Menunggu Persetujuan - ' . get_bloginfo('name');
        
        $message = "Ada kesaksian baru yang memerlukan persetujuan di website " . get_bloginfo('name') . "\n\n";
        $message .= "Judul: " . $post->post_title . "\n";
        $message .= "Dari: " . $nama . " (" . $email . ")\n";
        $message .= "Tanggal: " . date('d M Y H:i:s') . "\n\n";
        $message .= "Untuk meninjau dan menerbitkan kesaksian ini, kunjungi:\n";
        $message .= admin_url('post.php?post=' . $post_id . '&action=edit') . "\n\n";
        $message .= "Atau lihat semua kesaksian pending di:\n";
        $message .= admin_url('edit.php?post_type=kesaksian&post_status=pending');
        
        wp_mail($admin_email, $subject, $message);
    }
}
add_action('wp_insert_post', 'elshadaifm_notify_admin_new_kesaksian');

// Rate limiting function to prevent spam
function elshadaifm_check_submission_rate_limit() {
    if (!isset($_SERVER['REMOTE_ADDR'])) {
        return false;
    }
    
    $ip = $_SERVER['REMOTE_ADDR'];
    $transient_key = 'kesaksian_submit_' . md5($ip);
    
    // Check if IP has submitted in last 30 minutes
    if (get_transient($transient_key)) {
        return true; // Rate limited
    }
    
    // Set transient for 30 minutes
    set_transient($transient_key, time(), 30 * MINUTE_IN_SECONDS);
    return false;
}

// Security enhancement: Add honeypot field (invisible field to catch bots)
function elshadaifm_add_honeypot_validation() {
    if (isset($_POST['website_url']) && !empty($_POST['website_url'])) {
        // This is likely a bot submission
        wp_die('Security check failed.', 'Security Error', array('response' => 403));
    }
}

/**
 * Instagram Gallery Functions
 */

// Fetch Instagram posts without API
function elshadaifm_fetch_instagram_posts($username = 'elshaddai_radio', $limit = 12) {
    // Check if we have cached data (cache for 30 minutes)
    $cache_key = 'instagram_posts_' . $username;
    $cached_posts = get_transient($cache_key);
    
    if ($cached_posts !== false) {
        return $cached_posts;
    }
    
    $posts = array();
    $url = "https://www.instagram.com/$username/";
    
    // Enhanced cURL setup with better headers and options
    $ch = curl_init();
    curl_setopt_array($ch, array(
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_CONNECTTIMEOUT => 15,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        CURLOPT_HTTPHEADER => array(
            'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language: en-US,en;q=0.9,id;q=0.8',
            'Accept-Encoding: gzip, deflate, br',
            'Cache-Control: no-cache',
            'Pragma: no-cache',
            'Sec-Fetch-Dest: document',
            'Sec-Fetch-Mode: navigate',
            'Sec-Fetch-Site: none',
            'Sec-Fetch-User: ?1',
            'Upgrade-Insecure-Requests: 1'
        ),
        CURLOPT_ENCODING => '',
        CURLOPT_COOKIEJAR => '',
        CURLOPT_COOKIEFILE => ''
    ));
    
    $html = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    error_log("Instagram fetch - HTTP Code: $http_code, Error: $error");
    
    if ($http_code === 200 && $html && empty($error)) {
        // Try different patterns to extract Instagram data
        $patterns = array(
            '/window\._sharedData\s*=\s*({.*?});/',
            '/window\.__additionalDataLoaded\([^,]+,\s*({.*?})\s*\)/',
            '/"ProfilePage"\s*:\s*\[\s*({.*?})\s*\]/',
        );
        
        foreach ($patterns as $pattern) {
            preg_match($pattern, $html, $matches);
            if (!empty($matches[1])) {
                $data = json_decode($matches[1], true);
                if ($data) {
                    $posts = elshadaifm_parse_instagram_data($data, $limit);
                    if (!empty($posts)) {
                        break;
                    }
                }
            }
        }
        
        // Alternative: Try to find JSON-LD structured data
        if (empty($posts)) {
            preg_match('/<script type="application\/ld\+json">(.*?)<\/script>/s', $html, $json_matches);
            if (!empty($json_matches[1])) {
                $json_data = json_decode($json_matches[1], true);
                if ($json_data && isset($json_data['@graph'])) {
                    foreach ($json_data['@graph'] as $item) {
                        if (isset($item['@type']) && $item['@type'] === 'ImageObject') {
                            $posts[] = array(
                                'id' => uniqid(),
                                'shortcode' => '',
                                'caption' => $item['caption'] ?? '',
                                'image' => $item['url'] ?? '',
                                'thumbnail' => $item['thumbnailUrl'] ?? $item['url'] ?? '',
                                'is_video' => false,
                                'timestamp' => strtotime($item['datePublished'] ?? 'now'),
                                'likes' => 0,
                                'comments' => 0,
                                'permalink' => "https://www.instagram.com/$username/"
                            );
                        }
                    }
                }
            }
        }
        
        // Try to extract images directly from HTML
        if (empty($posts)) {
            preg_match_all('/<meta property="og:image" content="([^"]+)"/', $html, $img_matches);
            if (!empty($img_matches[1])) {
                foreach (array_slice($img_matches[1], 0, $limit) as $i => $image_url) {
                    $posts[] = array(
                        'id' => 'direct_' . $i,
                        'shortcode' => '',
                        'caption' => "Post dari @$username",
                        'image' => $image_url,
                        'thumbnail' => $image_url,
                        'is_video' => false,
                        'timestamp' => time() - ($i * 3600),
                        'likes' => 0,
                        'comments' => 0,
                        'permalink' => "https://www.instagram.com/$username/"
                    );
                }
            }
        }
    }
    
    // Log the result for debugging
    error_log("Instagram posts found: " . count($posts));
    
    // Cache the results for 30 minutes (shorter cache for more frequent updates)
    if (!empty($posts)) {
        set_transient($cache_key, $posts, 30 * MINUTE_IN_SECONDS);
    }
    
    return $posts;
}

// Parse Instagram data from different JSON structures
function elshadaifm_parse_instagram_data($data, $limit = 12) {
    $posts = array();
    
    // Try different data structures
    $media_edges = null;
    
    if (isset($data['entry_data']['ProfilePage'][0]['graphql']['user']['edge_owner_to_timeline_media']['edges'])) {
        $media_edges = $data['entry_data']['ProfilePage'][0]['graphql']['user']['edge_owner_to_timeline_media']['edges'];
    } elseif (isset($data['graphql']['user']['edge_owner_to_timeline_media']['edges'])) {
        $media_edges = $data['graphql']['user']['edge_owner_to_timeline_media']['edges'];
    } elseif (isset($data['user']['edge_owner_to_timeline_media']['edges'])) {
        $media_edges = $data['user']['edge_owner_to_timeline_media']['edges'];
    } elseif (isset($data['data']['user']['edge_owner_to_timeline_media']['edges'])) {
        $media_edges = $data['data']['user']['edge_owner_to_timeline_media']['edges'];
    }
    
    if ($media_edges) {
        foreach (array_slice($media_edges, 0, $limit) as $edge) {
            if (!isset($edge['node'])) continue;
            
            $node = $edge['node'];
            
            $post = array(
                'id' => $node['id'] ?? uniqid(),
                'shortcode' => $node['shortcode'] ?? '',
                'caption' => '',
                'image' => $node['display_url'] ?? $node['thumbnail_src'] ?? '',
                'thumbnail' => $node['thumbnail_src'] ?? $node['display_url'] ?? '',
                'is_video' => $node['is_video'] ?? false,
                'timestamp' => $node['taken_at_timestamp'] ?? time(),
                'likes' => $node['edge_liked_by']['count'] ?? 0,
                'comments' => $node['edge_media_to_comment']['count'] ?? 0,
                'permalink' => isset($node['shortcode']) ? "https://www.instagram.com/p/{$node['shortcode']}/" : "https://www.instagram.com/elshaddai_radio/"
            );
            
            // Extract caption
            if (isset($node['edge_media_to_caption']['edges'][0]['node']['text'])) {
                $post['caption'] = $node['edge_media_to_caption']['edges'][0]['node']['text'];
            } elseif (isset($node['accessibility_caption'])) {
                $post['caption'] = $node['accessibility_caption'];
            }
            
            if (!empty($post['image'])) {
                $posts[] = $post;
            }
        }
    }
    
    return $posts;
}


// AJAX handler for loading Instagram posts
function elshadaifm_ajax_load_instagram() {
    check_ajax_referer('elshadaifm_nonce', 'nonce');
    
    $posts = elshadaifm_fetch_instagram_posts();
    
    if (!empty($posts)) {
        wp_send_json_success($posts);
    } else {
        wp_send_json_error('Tidak dapat mengambil konten dari Instagram saat ini. Instagram mungkin sedang memblokir akses atau struktur halaman berubah.');
    }
}
add_action('wp_ajax_load_instagram', 'elshadaifm_ajax_load_instagram');
add_action('wp_ajax_nopriv_load_instagram', 'elshadaifm_ajax_load_instagram');

// Debug function for Instagram scraping
function elshadaifm_debug_instagram() {
    if (!current_user_can('manage_options')) {
        return;
    }
    
    if (isset($_GET['debug_instagram']) && $_GET['debug_instagram'] === '1') {
        echo '<h3>Instagram Debug Information</h3>';
        
        $posts = elshadaifm_fetch_instagram_posts('elshaddai_radio', 3);
        
        echo '<p>Posts found: ' . count($posts) . '</p>';
        
        if (!empty($posts)) {
            echo '<pre>' . print_r($posts, true) . '</pre>';
        } else {
            echo '<p>No posts found. Check error logs for more details.</p>';
            
            // Try basic cURL test
            $url = "https://www.instagram.com/elshaddai_radio/";
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
            
            $html = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            echo '<h4>Basic cURL test:</h4>';
            echo '<p>HTTP Code: ' . $http_code . '</p>';
            echo '<p>Error: ' . ($error ?: 'None') . '</p>';
            echo '<p>Response length: ' . strlen($html) . ' characters</p>';
            
            if ($html && strpos($html, 'elshaddai_radio') !== false) {
                echo '<p>✅ Instagram profile page loaded successfully</p>';
                
                // Show some patterns we're looking for
                echo '<h4>Looking for data patterns:</h4>';
                
                if (preg_match('/window\._sharedData\s*=\s*({.*?});/', $html)) {
                    echo '<p>✅ Found window._sharedData</p>';
                } else {
                    echo '<p>❌ No window._sharedData found</p>';
                }
                
                if (preg_match('/<script type="application\/ld\+json">(.*?)<\/script>/s', $html)) {
                    echo '<p>✅ Found JSON-LD structured data</p>';
                } else {
                    echo '<p>❌ No JSON-LD found</p>';
                }
                
                if (preg_match('/<meta property="og:image" content="([^"]+)"/', $html)) {
                    echo '<p>✅ Found Open Graph images</p>';
                } else {
                    echo '<p>❌ No Open Graph images found</p>';
                }
                
            } else {
                echo '<p>❌ Failed to load Instagram profile or profile not found</p>';
            }
        }
        
        die();
    }
}
add_action('wp_loaded', 'elshadaifm_debug_instagram');

// Clear Instagram cache function
function elshadaifm_clear_instagram_cache() {
    if (!current_user_can('manage_options')) {
        return;
    }
    
    if (isset($_GET['clear_instagram_cache']) && $_GET['clear_instagram_cache'] === '1') {
        delete_transient('instagram_posts_elshaddai_radio');
        echo '<p>Instagram cache cleared!</p>';
        echo '<p><a href="' . admin_url('?debug_instagram=1') . '">Debug Instagram</a></p>';
        die();
    }
}
add_action('wp_loaded', 'elshadaifm_clear_instagram_cache');

// Helper function to format time ago
function elshadaifm_time_ago($timestamp) {
    $time_diff = time() - $timestamp;
    
    if ($time_diff < 60) {
        return 'Baru saja';
    } elseif ($time_diff < 3600) {
        $minutes = floor($time_diff / 60);
        return $minutes . ' menit yang lalu';
    } elseif ($time_diff < 86400) {
        $hours = floor($time_diff / 3600);
        return $hours . ' jam yang lalu';
    } elseif ($time_diff < 604800) {
        $days = floor($time_diff / 86400);
        return $days . ' hari yang lalu';
    } elseif ($time_diff < 2419200) {
        $weeks = floor($time_diff / 604800);
        return $weeks . ' minggu yang lalu';
    } else {
        return date('d M Y', $timestamp);
    }
}


