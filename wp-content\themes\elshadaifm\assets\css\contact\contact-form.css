/*
 * Contact Form Styles
 * ElshadaiFM Theme - Contact Page
 */

/* ==========================================================================
   CONTACT GRID LAYOUT
   ========================================================================== */

.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: start;
}

/* ==========================================================================
   CONTACT FORM STYLES
   ========================================================================== */

.contact-form-container {
    background: rgba(255, 255, 255, 0.1);
    padding: 50px;
    border-radius: 24px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.contact-form {
    margin-top: 40px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 25px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    color: white;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-group input,
.form-group textarea {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    padding: 15px 20px;
    color: white;
    font-size: 16px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.6);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
    font-family: inherit;
}

.form-actions {
    margin-top: 30px;
    text-align: center;
}

.submit-btn {
    background: white;
    color: #8B5CF6;
    border: none;
    padding: 18px 36px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 16px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

.submit-btn:hover {
    background: #f8fafc;
    color: #7C3AED;
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(255, 255, 255, 0.3);
}

.submit-btn .arrow {
    font-size: 18px;
    transition: transform 0.3s ease;
}

.submit-btn:hover .arrow {
    transform: translateX(5px);
}

/* Form Message Styles */
.form-message {
    margin-top: 20px;
    padding: 15px 20px;
    border-radius: 12px;
    font-weight: 500;
    text-align: center;
}

.form-message.success {
    background: rgba(34, 197, 94, 0.2);
    border: 1px solid rgba(34, 197, 94, 0.3);
    color: #22c55e;
}

.form-message.error {
    background: rgba(239, 68, 68, 0.2);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #ef4444;
}

/* ==========================================================================
   CONTACT DETAILS STYLES
   ========================================================================== */

.contact-details-container {
    background: rgba(255, 255, 255, 0.1);
    padding: 50px;
    border-radius: 24px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.contact-info-grid {
    display: grid;
    gap: 30px;
    margin-bottom: 40px;
}

.contact-info-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 25px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.contact-info-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.info-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.info-content h4 {
    color: white;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
}

.info-content p {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 5px;
    line-height: 1.5;
}

/* Office Hours Styles */
.office-hours {
    margin-bottom: 40px;
}

.office-hours h4 {
    color: white;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    text-align: center;
}

.hours-grid {
    display: grid;
    gap: 15px;
}

.hours-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.hours-item .day {
    color: white;
    font-weight: 500;
}

.hours-item .time {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
}

/* Social Media Styles */
.contact-social h4 {
    color: white;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    text-align: center;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.social-link {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.3);
    text-decoration: none;
}

.social-link.facebook {
    background: linear-gradient(135deg, #1877f2, #166fe5);
}

.social-link.youtube {
    background: linear-gradient(135deg, #ff0000, #cc0000);
}

.social-link.instagram {
    background: linear-gradient(135deg, #e4405f, #833ab4);
}

.social-link.tiktok {
    background: linear-gradient(135deg, #000000, #333333);
}

.social-link:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* Form Loading State */
.contact-form.loading {
    pointer-events: none;
    opacity: 0.7;
}

.contact-form.loading .submit-btn {
    background: #ccc;
    cursor: not-allowed;
}

/* Form Field Focus States */
.form-group.focused label {
    color: #ffffff;
    transform: translateY(-2px);
}

.form-group.focused input,
.form-group.focused textarea {
    border-color: rgba(255, 255, 255, 0.8);
    background: rgba(255, 255, 255, 0.2);
}

/* Form Validation States */
.form-group.error input,
.form-group.error textarea {
    border-color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
}

.form-group.error label {
    color: #ef4444;
}

.field-error {
    color: #ef4444;
    font-size: 12px;
    margin-top: 5px;
    display: block;
}

.form-group.success input,
.form-group.success textarea {
    border-color: #22c55e;
    background: rgba(34, 197, 94, 0.1);
}

/* Pulse Animation for Icons */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

.info-icon.pulse {
    animation: pulse 0.6s ease-in-out;
}

/* FAQ Item Click Effect */
.faq-item--clicked {
    transform: scale(0.98);
    transition: transform 0.1s ease;
}

/* ==========================================================================
   RESPONSIVE FORM STYLES
   ========================================================================== */

@media (max-width: 768px) {
    .contact-grid {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .contact-form-container,
    .contact-details-container {
        padding: 30px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .contact-info-item {
        padding: 20px;
    }

    .social-links {
        gap: 12px;
    }

    .social-link {
        width: 45px;
        height: 45px;
        font-size: 18px;
    }
}

@media (max-width: 480px) {
    .contact-form-container,
    .contact-details-container {
        padding: 25px;
    }

    .submit-btn {
        width: 100%;
        justify-content: center;
    }
}
