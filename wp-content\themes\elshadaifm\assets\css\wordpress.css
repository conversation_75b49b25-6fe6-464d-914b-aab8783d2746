/* ==========================================================================
   WORDPRESS SPECIFIC STYLES
   ========================================================================== */
.wp-block-group {
    margin-bottom: 2rem;
}

.wp-block-columns {
    display: flex;
    gap: 2rem;
}

.wp-block-column {
    flex: 1;
}

.aligncenter {
    text-align: center;
}

.alignleft {
    float: left;
    margin-right: 1rem;
}

.alignright {
    float: right;
    margin-left: 1rem;
}

.wp-caption {
    max-width: 100%;
}

.wp-caption-text {
    font-size: 14px;
    color: #64748b;
    text-align: center;
    margin-top: 10px;
}

/* Comments */
.comments-area {
    margin-top: 3rem;
    padding-top: 3rem;
    border-top: 1px solid #e2e8f0;
}

.comment-list {
    list-style: none;
    padding: 0;
}

.comment {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 10px;
}

.comment-author {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.comment-meta {
    font-size: 14px;
    color: #64748b;
    margin-bottom: 1rem;
}

/* Pagination */
.pagination a,
.pagination span {
    padding: 10px 15px;
    border: 1px solid #e2e8f0;
    border-radius: 5px;
    color: #64748b;
    text-decoration: none;
}

.pagination a:hover {
    background: #8B5CF6;
    color: white;
    border-color: #8B5CF6;
}

.pagination .current {
    background: #8B5CF6;
    color: white;
    border-color: #8B5CF6;
}
