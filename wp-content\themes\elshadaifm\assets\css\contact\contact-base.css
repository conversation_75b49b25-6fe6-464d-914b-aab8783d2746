/*
 * Contact Base Styles
 * ElshadaiFM Theme - Contact Page
 */

/* ==========================================================================
   CONTACT PAGE BASE LAYOUT
   ========================================================================== */

.content-area {
    background: linear-gradient(135deg, #8B5CF6, #7C3AED, #6D28D9);
    min-height: 100vh;
}

.site-main {
    padding: 0;
}

/* Contact Container - consistent with about page */
.contact-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px;
}

/* ==========================================================================
   CONTACT INTRODUCTION SECTION
   ========================================================================== */

.contact-intro {
    padding: 80px 0;
    background: transparent;
}

.contact-content-wrapper {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.contact-header {
    margin-bottom: 0;
}

.contact-badge {
    display: inline-block;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 12px 24px;
    border-radius: 50px;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 30px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.contact-title {
    font-size: 3.5rem;
    font-weight: 800;
    color: white;
    margin-bottom: 30px;
    line-height: 1.2;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.contact-subtitle {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    margin-bottom: 0;
    font-weight: 400;
}

/* ==========================================================================
   SECTION HEADERS - CONSISTENT WITH ABOUT PAGE
   ========================================================================== */

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 20px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-subtitle {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0;
    line-height: 1.6;
}

/* ==========================================================================
   CONTACT SECTIONS SPACING
   ========================================================================== */

.contact-info-section {
    padding: 80px 0;
    background: transparent;
}

.map-section {
    padding: 80px 0;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
}

.faq-section {
    padding: 80px 0;
    background: transparent;
}

.cta-section {
    padding: 80px 0;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
}

/* ==========================================================================
   CTA SECTION - CONSISTENT WITH ABOUT PAGE
   ========================================================================== */

.cta-content {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 40px;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 60px;
    border-radius: 24px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.cta-text {
    text-align: left;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 20px;
    line-height: 1.2;
}

.cta-subtitle {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0;
    line-height: 1.6;
}

.cta-button {
    background: white;
    color: #8B5CF6;
    padding: 18px 36px;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-block;
    font-size: 16px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.cta-button:hover {
    background: #f8fafc;
    color: #7C3AED;
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(255, 255, 255, 0.3);
}

/* ==========================================================================
   RESPONSIVE BASE STYLES
   ========================================================================== */

@media (max-width: 768px) {
    .contact-container {
        padding: 0 20px;
    }
    
    .contact-intro {
        padding: 60px 0;
    }
    
    .contact-title {
        font-size: 2.5rem;
    }
    
    .contact-info-section,
    .map-section,
    .faq-section,
    .cta-section {
        padding: 60px 0;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .cta-content {
        grid-template-columns: 1fr;
        text-align: center;
        padding: 40px;
        gap: 30px;
    }
    
    .cta-text {
        text-align: center;
    }
    
    .cta-title {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .contact-container {
        padding: 0 15px;
    }
    
    .contact-title {
        font-size: 2rem;
    }
    
    .section-title {
        font-size: 1.8rem;
    }
    
    .cta-content {
        padding: 30px;
    }
    
    .cta-title {
        font-size: 1.8rem;
    }
}
