/* ==========================================================================
   CONTACT - BASE STYLES & CONTAINER
   ========================================================================== */

/* Container & Spacing System - consistent with about us */
.contact-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Section Headers - consistent with about us */
.section-header {
    text-align: center;
    max-width: 700px;
    margin: 0 auto 80px;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(139, 92, 246, 0.1);
    color: #8B5CF6;
    padding: 6px 16px;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 20px;
}

.section-title {
    font-family: 'Poppins', sans-serif;
    font-size: clamp(2rem, 4vw, 3.5rem);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 20px;
    color: #1e293b;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #64748b;
    line-height: 1.6;
    margin: 0;
}

/* ==========================================================================
   CONTACT INTRODUCTION SECTION - consistent with about us
   ========================================================================== */

/* Professional Introduction Section */
.contact-intro {
    padding: 80px 0;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    position: relative;
    overflow: hidden;
    opacity: 1 !important; /* Always visible */
    transform: translateY(0) !important; /* No parallax animation */
    will-change: auto; /* Remove transform optimization */
}

.contact-intro::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -10%;
    width: 600px;
    height: 600px;
    background: radial-gradient(circle, rgba(139, 92, 246, 0.08) 0%, transparent 70%);
    border-radius: 50%;
    pointer-events: none;
}

.contact-intro::after {
    content: '';
    position: absolute;
    bottom: -50%;
    left: -10%;
    width: 500px;
    height: 500px;
    background: radial-gradient(circle, rgba(124, 58, 237, 0.06) 0%, transparent 70%);
    border-radius: 50%;
    pointer-events: none;
}

.contact-content-wrapper {
    max-width: 900px;
    margin: 0 auto;
    text-align: center;
    position: relative;
    z-index: 2;
}

.contact-header {
    margin-bottom: 60px;
}

.contact-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(139, 92, 246, 0.1);
    color: #8B5CF6;
    padding: 6px 16px;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 20px;
}

.contact-title {
    font-family: 'Poppins', sans-serif;
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 30px;
    color: #1e293b;
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.contact-subtitle {
    font-size: 1.25rem;
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 0;
    font-weight: 400;
}

/* ==========================================================================
   SECTION HEADERS - CONSISTENT WITH ABOUT PAGE
   ========================================================================== */

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 20px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-subtitle {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0;
    line-height: 1.6;
}

/* ==========================================================================
   CONTACT SECTIONS SPACING
   ========================================================================== */

.contact-info-section {
    padding: 80px 0;
    background: #ffffff;
}

.map-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
}

.faq-section {
    padding: 80px 0;
    background: #ffffff;
}

.cta-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #8B5CF6, #7C3AED, #6D28D9);
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -20%;
    width: 800px;
    height: 800px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    pointer-events: none;
}

/* ==========================================================================
   CTA SECTION - CONSISTENT WITH ABOUT PAGE
   ========================================================================== */

.cta-content {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 40px;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 60px;
    border-radius: 24px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    z-index: 2;
}

.cta-text {
    text-align: left;
}

.cta-title {
    font-family: 'Poppins', sans-serif;
    font-size: clamp(2rem, 4vw, 2.5rem);
    font-weight: 700;
    color: white;
    margin-bottom: 20px;
    line-height: 1.2;
}

.cta-subtitle {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0;
    line-height: 1.6;
}

.cta-button {
    background: white;
    color: #8B5CF6;
    padding: 18px 36px;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-block;
    font-size: 16px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.cta-button:hover {
    background: #f8fafc;
    color: #7C3AED;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* ==========================================================================
   RESPONSIVE BASE STYLES
   ========================================================================== */

@media (max-width: 768px) {
    .contact-container {
        padding: 0 20px;
    }
    
    .contact-intro {
        padding: 60px 0;
    }
    
    .contact-title {
        font-size: 2.5rem;
    }
    
    .contact-info-section,
    .map-section,
    .faq-section,
    .cta-section {
        padding: 60px 0;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .cta-content {
        grid-template-columns: 1fr;
        text-align: center;
        padding: 40px;
        gap: 30px;
    }
    
    .cta-text {
        text-align: center;
    }
    
    .cta-title {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .contact-container {
        padding: 0 15px;
    }
    
    .contact-title {
        font-size: 2rem;
    }
    
    .section-title {
        font-size: 1.8rem;
    }
    
    .cta-content {
        padding: 30px;
    }
    
    .cta-title {
        font-size: 1.8rem;
    }
}
