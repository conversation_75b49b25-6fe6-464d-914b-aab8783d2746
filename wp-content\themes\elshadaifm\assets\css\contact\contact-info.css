/*
 * Contact Info Styles
 * ElshadaiFM Theme - Contact Page
 */

/* ==========================================================================
   MAP SECTION STYLES
   ========================================================================== */

.map-container {
    background: white;
    padding: 30px;
    border-radius: 24px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(226, 232, 240, 0.6);
}

.map-placeholder {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.map-placeholder iframe {
    width: 100%;
    height: 450px;
    border: none;
    border-radius: 16px;
}

/* ==========================================================================
   FAQ SECTION STYLES
   ========================================================================== */

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.faq-item {
    background: white;
    padding: 40px;
    border-radius: 24px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(226, 232, 240, 0.6);
    transition: all 0.3s ease;
    text-align: center;
}

.faq-item:hover {
    background: #f8fafc;
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.faq-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 28px;
    margin: 0 auto 25px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.faq-item:hover .faq-icon {
    background: linear-gradient(135deg, #7C3AED, #6D28D9);
    transform: scale(1.1);
}

.faq-item h4 {
    color: #1e293b;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    line-height: 1.3;
}

.faq-item p {
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 0;
}

/* ==========================================================================
   ENHANCED CONTACT INFO ITEMS
   ========================================================================== */

.contact-info-item {
    position: relative;
    overflow: hidden;
}

.contact-info-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.contact-info-item:hover::before {
    left: 100%;
}

/* Enhanced Info Icons */
.info-icon {
    position: relative;
    overflow: hidden;
}

.info-icon::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
}

.contact-info-item:hover .info-icon::before {
    width: 100%;
    height: 100%;
}

/* ==========================================================================
   INTERACTIVE ELEMENTS
   ========================================================================== */

/* Animated Gradient Borders */
.contact-form-container,
.contact-details-container,
.map-container,
.faq-item {
    position: relative;
}

.contact-form-container::before,
.contact-details-container::before,
.map-container::before,
.faq-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: inherit;
    padding: 1px;
    background: linear-gradient(45deg, 
        rgba(255, 255, 255, 0.3), 
        rgba(255, 255, 255, 0.1), 
        rgba(255, 255, 255, 0.3)
    );
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.contact-form-container:hover::before,
.contact-details-container:hover::before,
.map-container:hover::before,
.faq-item:hover::before {
    opacity: 1;
}

/* Floating Animation for FAQ Icons */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.faq-icon {
    animation: float 3s ease-in-out infinite;
}

.faq-item:nth-child(2) .faq-icon {
    animation-delay: 0.5s;
}

.faq-item:nth-child(3) .faq-icon {
    animation-delay: 1s;
}

.faq-item:nth-child(4) .faq-icon {
    animation-delay: 1.5s;
}

/* ==========================================================================
   RESPONSIVE INFO STYLES
   ========================================================================== */

@media (max-width: 768px) {
    .faq-grid {
        grid-template-columns: 1fr;
        gap: 25px;
    }
    
    .faq-item {
        padding: 30px;
    }
    
    .faq-icon {
        width: 60px;
        height: 60px;
        font-size: 24px;
        margin-bottom: 20px;
    }
    
    .faq-item h4 {
        font-size: 18px;
        margin-bottom: 15px;
    }
    
    .map-container {
        padding: 20px;
    }
    
    .map-placeholder iframe {
        height: 300px;
    }
}

@media (max-width: 480px) {
    .faq-item {
        padding: 25px;
    }
    
    .faq-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
    
    .faq-item h4 {
        font-size: 16px;
    }
    
    .map-placeholder iframe {
        height: 250px;
    }
}

/* ==========================================================================
   ACCESSIBILITY ENHANCEMENTS
   ========================================================================== */

/* Focus states for better accessibility */
.faq-item:focus-within {
    outline: 2px solid rgba(255, 255, 255, 0.5);
    outline-offset: 2px;
}

.social-link:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5);
    outline-offset: 2px;
}

/* Reduced motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    .faq-icon {
        animation: none;
    }
    
    .contact-info-item::before,
    .info-icon::before {
        transition: none;
    }
    
    .faq-item,
    .contact-info-item,
    .social-link {
        transition: none;
    }
}
