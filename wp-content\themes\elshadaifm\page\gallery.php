<?php
/**
 * Template Name: Gallery
 * 
 * Instagram Gallery Page Template
 *
 * @package Elshadaifm
 */

get_header(); ?>

<div id="primary" class="content-area">
    <main id="main" class="site-main">
        
        <div class="gallery-container">
            <div class="gallery-header">
                <h1 class="gallery-title">Gallery Instagram</h1>
                <p class="gallery-subtitle">Foto dan video terbaru dari @elshaddai_radio</p>
            </div>

            <div id="instagram-gallery" class="instagram-grid">
                <div class="loading-spinner">
                    <div class="spinner"></div>
                    <p>Memuat gallery...</p>
                </div>
            </div>

            <div class="gallery-footer">
                <a href="https://www.instagram.com/elshaddai_radio/" target="_blank" rel="noopener" class="instagram-link">
                    <span class="instagram-icon">📷</span>
                    Kunjungi Instagram Kami
                </a>
            </div>
        </div>

    </main>
</div>

<style>
.gallery-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
}

.gallery-header {
    text-align: center;
    margin-bottom: 40px;
}

.gallery-title {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 10px;
    font-weight: 700;
}

.gallery-subtitle {
    font-size: 1.1rem;
    color: #666;
    margin: 0;
}

.instagram-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
    justify-items: center;
}

.instagram-post {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    width: 100%;
    max-width: 380px;
    display: flex;
    flex-direction: column;
}

.instagram-post:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.instagram-media {
    position: relative;
    width: 100%;
    height: 320px;
    overflow: hidden;
}

.instagram-media img,
.instagram-media video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.3s ease;
    display: block;
}

.instagram-post:hover .instagram-media img,
.instagram-post:hover .instagram-media video {
    transform: scale(1.05);
}

.media-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(225, 48, 108, 0.8), rgba(255, 204, 119, 0.8));
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
}

.instagram-post:hover .media-overlay {
    opacity: 1;
}

.instagram-content {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.instagram-text {
    color: #333;
    line-height: 1.6;
    margin: 0 0 15px 0;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.instagram-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #666;
    font-size: 0.9rem;
}

.instagram-date {
    display: flex;
    align-items: center;
    gap: 5px;
}

.instagram-link-post {
    color: #E1306C;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.instagram-link-post:hover {
    color: #C13584;
}

.video-indicator {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 5px 8px;
    border-radius: 15px;
    font-size: 0.8rem;
    z-index: 2;
}

.instagram-stats {
    display: flex;
    gap: 15px;
    font-size: 0.85rem;
}

.instagram-stats span {
    display: flex;
    align-items: center;
    gap: 3px;
}

.instagram-actions {
    margin-top: auto;
    padding-top: 15px;
    border-top: 1px solid #f0f0f0;
}

.retry-button {
    display: inline-block;
    padding: 10px 20px;
    background: #E1306C;
    color: white;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-right: 15px;
}

.retry-button:hover {
    background: #C13584;
    transform: translateY(-2px);
}

.loading-spinner {
    grid-column: 1/-1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: #666;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #E1306C;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-message {
    grid-column: 1/-1;
    text-align: center;
    padding: 40px 20px;
    color: #666;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #E1306C;
}

.gallery-footer {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.instagram-link {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    background: linear-gradient(45deg, #E1306C, #C13584);
    color: white;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 600;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.instagram-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(225, 48, 108, 0.3);
    color: white;
}

.instagram-icon {
    font-size: 1.2rem;
}

@media (max-width: 1024px) {
    .instagram-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
    }

    .instagram-post {
        max-width: 350px;
    }

    .instagram-media {
        height: 300px;
    }
}

@media (max-width: 768px) {
    .gallery-container {
        padding: 20px 15px;
    }

    .gallery-title {
        font-size: 2rem;
    }

    .instagram-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 20px;
    }

    .instagram-post {
        max-width: 100%;
    }

    .instagram-media {
        height: 280px;
    }
}

@media (max-width: 480px) {
    .gallery-title {
        font-size: 1.8rem;
    }

    .instagram-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .instagram-content {
        padding: 15px;
    }

    .instagram-media {
        height: 250px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const galleryContainer = document.getElementById('instagram-gallery');
    
    function loadInstagramContent() {
        // Make AJAX request to load Instagram posts
        fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'load_instagram',
                nonce: '<?php echo wp_create_nonce('elshadaifm_nonce'); ?>'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayPosts(data.data);
            } else {
                throw new Error(data.data || 'Failed to load posts');
            }
        })
        .catch(error => {
            console.error('Error loading Instagram content:', error);
            showError();
        });
    }
    
    function displayPosts(posts) {
        if (posts.length === 0) {
            galleryContainer.innerHTML = `
                <div class="error-message">
                    <h3>Tidak ada konten tersedia</h3>
                    <p>Silakan kunjungi halaman Instagram kami untuk melihat konten terbaru.</p>
                </div>
            `;
            return;
        }
        
        const postsHTML = posts.map(post => {
            // Format timestamp
            const timeAgo = formatTimeAgo(post.timestamp);
            
            // Truncate caption
            const caption = post.caption.length > 150 
                ? post.caption.substring(0, 150) + '...' 
                : post.caption;
            
            return `
                <article class="instagram-post">
                    <div class="instagram-media">
                        <img src="${post.image}" alt="Instagram post" loading="lazy" onerror="this.src='https://via.placeholder.com/400x400/E1306C/ffffff?text=ElshaddaiRadio'">
                        <div class="media-overlay">
                            <span>Lihat di Instagram</span>
                        </div>
                        ${post.is_video ? '<div class="video-indicator">📹</div>' : ''}
                    </div>
                    <div class="instagram-content">
                        <p class="instagram-text">${caption}</p>
                        <div class="instagram-meta">
                            <span class="instagram-date">
                                <span>🕒</span>
                                ${timeAgo}
                            </span>
                            <div class="instagram-stats">
                                <span>❤️ ${post.likes || 0}</span>
                                <span>💬 ${post.comments || 0}</span>
                            </div>
                        </div>
                        <div class="instagram-actions">
                            <a href="${post.permalink || 'https://www.instagram.com/elshaddai_radio/'}" target="_blank" rel="noopener" class="instagram-link-post">
                                Lihat di Instagram
                            </a>
                        </div>
                    </div>
                </article>
            `;
        }).join('');
        
        galleryContainer.innerHTML = postsHTML;
    }
    
    function formatTimeAgo(timestamp) {
        const now = Math.floor(Date.now() / 1000);
        const diff = now - timestamp;
        
        if (diff < 60) {
            return 'Baru saja';
        } else if (diff < 3600) {
            const minutes = Math.floor(diff / 60);
            return minutes + ' menit yang lalu';
        } else if (diff < 86400) {
            const hours = Math.floor(diff / 3600);
            return hours + ' jam yang lalu';
        } else if (diff < 604800) {
            const days = Math.floor(diff / 86400);
            return days + ' hari yang lalu';
        } else if (diff < 2419200) {
            const weeks = Math.floor(diff / 604800);
            return weeks + ' minggu yang lalu';
        } else {
            return new Date(timestamp * 1000).toLocaleDateString('id-ID', { 
                day: 'numeric', 
                month: 'short', 
                year: 'numeric' 
            });
        }
    }
    
    function showError() {
        galleryContainer.innerHTML = `
            <div class="error-message">
                <h3>Tidak dapat memuat konten</h3>
                <p>Maaf, terjadi masalah saat memuat konten Instagram. Silakan coba lagi nanti atau kunjungi halaman Instagram kami langsung.</p>
                <button onclick="location.reload()" class="retry-button">Coba Lagi</button>
                <a href="https://www.instagram.com/elshaddai_radio/" target="_blank" rel="noopener" class="instagram-link" style="margin-top: 15px; display: inline-flex;">
                    <span class="instagram-icon">📷</span>
                    Kunjungi Instagram
                </a>
            </div>
        `;
    }
    
    // Initialize gallery
    loadInstagramContent();
});
</script>

<?php get_footer(); ?>