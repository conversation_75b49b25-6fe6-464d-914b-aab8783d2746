/*
 * DEPRECATED: This file is no longer used
 * Contact page now uses modular CSS files in /assets/css/contact/
 * This file is kept for reference only and should not be loaded
 */

/* DEPRECATED - DO NOT USE */

.site-main {
    padding: 0;
}

/* Contact Container - consistent with about page */
.contact-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px;
}

/* Contact Introduction Section */
.contact-intro {
    padding: 80px 0;
    background: transparent;
}

.contact-content-wrapper {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.contact-header {
    margin-bottom: 0;
}

.contact-badge {
    display: inline-block;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 12px 24px;
    border-radius: 50px;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 30px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.contact-title {
    font-size: 3.5rem;
    font-weight: 800;
    color: white;
    margin-bottom: 30px;
    line-height: 1.2;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.contact-subtitle {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    margin-bottom: 0;
    font-weight: 400;
}

/* Section Headers - consistent with about page */
.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 20px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-subtitle {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0;
    line-height: 1.6;
}

/* Contact Sections Spacing */
.contact-info-section {
    padding: 80px 0;
    background: transparent;
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: start;
}

/* Contact Form - glassmorphism design */
.contact-form-container {
    background: rgba(255, 255, 255, 0.1);
    padding: 50px;
    border-radius: 24px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.contact-form {
    margin-top: 40px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 25px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    color: white;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-group input,
.form-group textarea {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    padding: 15px 20px;
    color: white;
    font-size: 16px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.6);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
    font-family: inherit;
}

.form-actions {
    margin-top: 30px;
    text-align: center;
}

.submit-btn {
    background: white;
    color: #8B5CF6;
    border: none;
    padding: 18px 36px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 16px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

.submit-btn:hover {
    background: #f8fafc;
    color: #7C3AED;
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(255, 255, 255, 0.3);
}

.submit-btn .arrow {
    font-size: 18px;
    transition: transform 0.3s ease;
}

.submit-btn:hover .arrow {
    transform: translateX(5px);
}

.form-message {
    margin-top: 20px;
    padding: 15px 20px;
    border-radius: 12px;
    font-weight: 500;
    text-align: center;
}

.form-message.success {
    background: rgba(34, 197, 94, 0.2);
    border: 1px solid rgba(34, 197, 94, 0.3);
    color: #22c55e;
}

.form-message.error {
    background: rgba(239, 68, 68, 0.2);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #ef4444;
}

/* Contact Details - glassmorphism design */
.contact-details-container {
    background: rgba(255, 255, 255, 0.1);
    padding: 50px;
    border-radius: 24px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.contact-info-grid {
    display: grid;
    gap: 30px;
    margin-bottom: 40px;
}

.contact-info-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 25px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.contact-info-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.info-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.info-content h4 {
    color: white;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
}

.info-content p {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 5px;
    line-height: 1.5;
}

/* Office Hours */
.office-hours {
    margin-bottom: 40px;
}

.office-hours h4 {
    color: white;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    text-align: center;
}

.hours-grid {
    display: grid;
    gap: 15px;
}

.hours-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.hours-item .day {
    color: white;
    font-weight: 500;
}

.hours-item .time {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
}

/* Social Media */
.contact-social h4 {
    color: white;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    text-align: center;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.social-link {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.3);
    text-decoration: none;
}

.social-link.facebook {
    background: linear-gradient(135deg, #1877f2, #166fe5);
}

.social-link.youtube {
    background: linear-gradient(135deg, #ff0000, #cc0000);
}

.social-link.instagram {
    background: linear-gradient(135deg, #e4405f, #833ab4);
}

.social-link.tiktok {
    background: linear-gradient(135deg, #000000, #333333);
}

.social-link:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* Map Section */
.map-section {
    padding: 80px 0;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
}

.map-container {
    background: rgba(255, 255, 255, 0.1);
    padding: 30px;
    border-radius: 24px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.map-placeholder {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.map-placeholder iframe {
    width: 100%;
    height: 450px;
    border: none;
    border-radius: 16px;
}

/* FAQ Section */
.faq-section {
    padding: 80px 0;
    background: transparent;
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.faq-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 40px;
    border-radius: 24px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    text-align: center;
}

.faq-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.faq-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 28px;
    margin: 0 auto 25px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.faq-item:hover .faq-icon {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.2));
    transform: scale(1.1);
}

.faq-item h4 {
    color: white;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    line-height: 1.3;
}

.faq-item p {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    margin-bottom: 0;
}

/* CTA Section */
.cta-section {
    padding: 80px 0;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
}

.cta-content {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 40px;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 60px;
    border-radius: 24px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.cta-text {
    text-align: left;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 20px;
    line-height: 1.2;
}

.cta-subtitle {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0;
    line-height: 1.6;
}

.cta-button {
    background: white;
    color: #8B5CF6;
    padding: 18px 36px;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-block;
    font-size: 16px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.cta-button:hover {
    background: #f8fafc;
    color: #7C3AED;
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(255, 255, 255, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .contact-container {
        padding: 0 20px;
    }

    .contact-intro,
    .contact-info-section,
    .map-section,
    .faq-section,
    .cta-section {
        padding: 60px 0;
    }

    .contact-title {
        font-size: 2.5rem;
        line-height: 1.1;
    }

    .contact-subtitle {
        font-size: 1.1rem;
    }

    .section-title {
        font-size: 2rem;
        margin-bottom: 15px;
    }

    .section-subtitle {
        font-size: 1rem;
    }

    .contact-grid {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
        margin-bottom: 20px;
    }

    .contact-form-container,
    .contact-details-container {
        padding: 30px;
    }

    .faq-grid {
        grid-template-columns: 1fr;
        gap: 25px;
    }

    .faq-item {
        padding: 30px;
    }

    .faq-icon {
        width: 60px;
        height: 60px;
        font-size: 24px;
        margin-bottom: 20px;
    }

    .faq-item h4 {
        font-size: 18px;
        margin-bottom: 15px;
    }

    .map-container {
        padding: 20px;
    }

    .map-placeholder iframe {
        height: 300px;
    }

    .cta-content {
        grid-template-columns: 1fr;
        text-align: center;
        padding: 40px;
        gap: 30px;
    }

    .cta-text {
        text-align: center;
    }

    .cta-title {
        font-size: 2rem;
        margin-bottom: 15px;
    }

    .cta-subtitle {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .contact-container {
        padding: 0 15px;
    }

    .contact-intro,
    .contact-info-section,
    .map-section,
    .faq-section,
    .cta-section {
        padding: 50px 0;
    }

    .contact-title {
        font-size: 2rem;
        margin-bottom: 20px;
    }

    .contact-subtitle {
        font-size: 1rem;
    }

    .section-title {
        font-size: 1.8rem;
        margin-bottom: 12px;
    }

    .contact-badge {
        padding: 10px 20px;
        font-size: 12px;
        margin-bottom: 25px;
    }

    .contact-form-container,
    .contact-details-container {
        padding: 25px;
    }

    .contact-info-item {
        padding: 18px;
        gap: 12px;
    }

    .info-icon {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    .info-content h4 {
        font-size: 15px;
        margin-bottom: 5px;
    }

    .info-content p {
        font-size: 13px;
    }

    .hours-item {
        padding: 10px 14px;
    }

    .hours-item .day,
    .hours-item .time {
        font-size: 13px;
    }

    .social-link {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    .faq-item {
        padding: 25px;
    }

    .faq-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
        margin-bottom: 18px;
    }

    .faq-item h4 {
        font-size: 16px;
        margin-bottom: 12px;
    }

    .faq-item p {
        font-size: 13px;
        line-height: 1.5;
    }

    .map-container {
        padding: 15px;
    }

    .map-placeholder iframe {
        height: 250px;
    }

    .cta-content {
        padding: 30px;
        gap: 25px;
    }

    .cta-title {
        font-size: 1.8rem;
        margin-bottom: 12px;
    }

    .cta-subtitle {
        font-size: 0.95rem;
    }
}

/* Loading States */
.contact-form.loading .submit-btn {
    opacity: 0.7;
    pointer-events: none;
}

.contact-form.loading .submit-btn::after {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Print Styles */
@media print {
    .contact-page {
        background: white;
        color: black;
    }

    .contact-form,
    .submit-btn,
    .social-links {
        display: none;
    }

    .contact-form-container,
    .contact-details-container,
    .map-container,
    .faq-item {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}