/* Contact Page Styles */
.contact-page {
    padding: 80px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.contact-page .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 50px;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #fff;
    margin-bottom: 15px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.section-subtitle {
    font-size: 1.1rem;
    color: rgba(255,255,255,0.9);
    margin-bottom: 0;
}

/* Contact Info Section */
.contact-info-section {
    margin-bottom: 80px;
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: start;
}

/* Contact Form */
.contact-form-container {
    background: rgba(255,255,255,0.95);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
}

.form-group input,
.form-group textarea {
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #fff;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102,126,234,0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.form-actions {
    margin-top: 10px;
}

.submit-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102,126,234,0.3);
}

.submit-btn .arrow {
    transition: transform 0.3s ease;
}

.submit-btn:hover .arrow {
    transform: translateX(5px);
}

.form-message {
    padding: 15px;
    border-radius: 10px;
    margin-top: 20px;
    font-weight: 500;
}

.form-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.form-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Contact Details */
.contact-details-container {
    background: rgba(255,255,255,0.95);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.contact-info-grid {
    display: grid;
    gap: 25px;
    margin-bottom: 40px;
}

.contact-info-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 20px;
    background: rgba(102,126,234,0.05);
    border-radius: 15px;
    transition: all 0.3s ease;
}

.contact-info-item:hover {
    background: rgba(102,126,234,0.1);
    transform: translateY(-2px);
}

.info-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.info-content h4 {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
}

.info-content p {
    margin: 0 0 5px 0;
    color: #666;
    font-size: 0.95rem;
    line-height: 1.4;
}

/* Office Hours */
.office-hours {
    margin-bottom: 30px;
}

.office-hours h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
}

.hours-grid {
    display: grid;
    gap: 10px;
}

.hours-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: rgba(102,126,234,0.05);
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.hours-item .day {
    font-weight: 500;
    color: #333;
}

.hours-item .time {
    font-weight: 600;
    color: #667eea;
}

/* Social Media */
.contact-social h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
}

.social-links {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.social-link {
    width: 45px;
    height: 45px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    text-decoration: none;
}

.social-link.facebook {
    background: #1877f2;
}

.social-link.youtube {
    background: #ff0000;
}

.social-link.instagram {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
}

.social-link.tiktok {
    background: #000000;
}

.social-link:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* Map Section */
.map-section {
    margin-bottom: 80px;
}

.map-container {
    background: rgba(255,255,255,0.95);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.map-placeholder {
    border-radius: 15px;
    overflow: hidden;
    position: relative;
    height: 450px;
}

.map-placeholder iframe {
    width: 100%;
    height: 100%;
    border: none;
}

/* FAQ Section */
.faq-section {
    margin-bottom: 40px;
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.faq-item {
    background: rgba(255,255,255,0.95);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.faq-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.faq-item h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 1.2rem;
    font-weight: 600;
    line-height: 1.3;
}

.faq-item p {
    margin: 0;
    color: #666;
    font-size: 0.95rem;
    line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .contact-page {
        padding: 40px 0;
    }

    .contact-grid {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .contact-form-container,
    .contact-details-container,
    .map-container {
        padding: 25px;
    }

    .section-title {
        font-size: 2rem;
    }

    .faq-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .social-links {
        justify-content: center;
    }

    .map-placeholder {
        height: 300px;
    }
}

@media (max-width: 480px) {
    .contact-page .container {
        padding: 0 15px;
    }

    .contact-form-container,
    .contact-details-container,
    .map-container {
        padding: 20px;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .contact-info-item {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .info-icon {
        margin: 0 auto;
    }

    .hours-item {
        flex-direction: column;
        gap: 5px;
        text-align: center;
    }
}

/* Loading States */
.contact-form.loading .submit-btn {
    opacity: 0.7;
    pointer-events: none;
}

.contact-form.loading .submit-btn::after {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Print Styles */
@media print {
    .contact-page {
        background: white;
        color: black;
    }

    .contact-form,
    .submit-btn,
    .social-links {
        display: none;
    }

    .contact-form-container,
    .contact-details-container,
    .map-container,
    .faq-item {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}