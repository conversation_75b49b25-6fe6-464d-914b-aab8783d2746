/* ==========================================================================
   COMPONENT STYLES
   ========================================================================== */

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 30px;
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    color: white;
    border: none;
    border-radius: 25px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.btn:hover {
    background: linear-gradient(135deg, #7C3AED, #6D28D9);
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(139, 92, 246, 0.3);
    color: white;
}

.btn-white {
    background: white;
    color: #8B5CF6;
}

.btn-white:hover {
    background: #f8fafc;
    color: #7C3AED;
}

/* Section Titles */
.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 3rem;
    color: #1e293b;
    font-family: 'Poppins', sans-serif;
}

.section-title::after {
    content: '';
    display: block;
    width: 60px;
    height: 4px;
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    margin: 20px auto 0;
}

/* Cards */
.event-card {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.news-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.news-card:hover {
    transform: translateY(-10px);
}

.news-card-image {
    height: 200px;
    overflow: hidden;
}

.news-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.news-card:hover .news-card-image img {
    transform: scale(1.1);
}

.news-card-content {
    padding: 25px;
}

.news-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #1e293b;
}

.news-card-meta {
    color: #64748b;
    font-size: 14px;
    margin-bottom: 15px;
}

/* Team Member Cards */
.team-member {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 30px;
    backdrop-filter: blur(10px);
    transition: transform 0.3s ease;
}

.team-member:hover {
    transform: translateY(-10px);
}

.team-member-img {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    margin: 0 auto 20px;
    border: 4px solid rgba(255, 255, 255, 0.3);
}

.team-member h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.team-member-role {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    margin-bottom: 15px;
}

/* Chart Components */
.chart-container {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    margin-top: 50px;
}

.chart-entry {
    display: flex;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e2e8f0;
    transition: background-color 0.3s ease;
}

.chart-entry:hover {
    background-color: #f8fafc;
}

.chart-entry:last-child {
    border-bottom: none;
}

.chart-position {
    font-size: 2rem;
    font-weight: 700;
    color: #8B5CF6;
    margin-right: 20px;
    min-width: 60px;
}

.chart-song-info {
    flex: 1;
}

.chart-song-title {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 5px;
}

.chart-artist {
    color: #64748b;
    font-size: 14px;
}

.chart-album-art {
    width: 60px;
    height: 60px;
    border-radius: 10px;
    object-fit: cover;
    margin-left: 20px;
}

/* Top 3 Cards */
.top3-card {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    height: 300px;
}

.top3-card-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-size: cover;
    background-position: center;
}

.top3-card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(139, 92, 246, 0.8), rgba(124, 58, 237, 0.8));
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 30px;
}

.top3-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 15px;
}

/* Prayer Cards */
.prayer-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 30px;
    backdrop-filter: blur(10px);
}

.prayer-card h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 15px;
}

/* Video Player */
.video-player {
    background: #000;
    border-radius: 20px;
    overflow: hidden;
    margin: 30px 0;
    aspect-ratio: 16/9;
}

/* Forms */
.newsletter-form {
    max-width: 500px;
    margin: 30px auto 0;
    display: flex;
    gap: 15px;
}

.newsletter-input {
    flex: 1;
    padding: 15px 20px;
    border: none;
    border-radius: 25px;
    font-size: 16px;
}

.newsletter-input:focus {
    outline: none;
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
}

/* Event Speakers */
.event-speakers {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;
}

.speaker-img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #8B5CF6;
}

/* Social Links */
.social-links {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: #8B5CF6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: #7C3AED;
    transform: translateY(-3px);
}
