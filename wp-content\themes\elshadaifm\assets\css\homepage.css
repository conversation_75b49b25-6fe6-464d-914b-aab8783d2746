/* ==========================================================================
   HOMEPAGE SECTIONS
   ========================================================================== */

/* Next Event Section Specific Styles */
.next-event-section .event-card {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.next-event-section .event-speakers {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;
}

.next-event-section .speaker-img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #8B5CF6;
}

/* Latest News Section Specific Styles */
.latest-news-section .news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.latest-news-section .news-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.latest-news-section .news-card:hover {
    transform: translateY(-10px);
}

.latest-news-section .news-card-image {
    height: 200px;
    overflow: hidden;
}

.latest-news-section .news-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.latest-news-section .news-card:hover .news-card-image img {
    transform: scale(1.1);
}

.latest-news-section .news-card-content {
    padding: 25px;
}

.latest-news-section .news-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #1e293b;
}

.latest-news-section .news-card-meta {
    color: #64748b;
    font-size: 14px;
    margin-bottom: 15px;
}

/* Team Section Specific Styles */
.team-section .team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 50px;
    position: relative;
    z-index: 2;
}

.team-section .team-member {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 30px;
    backdrop-filter: blur(10px);
    transition: transform 0.3s ease;
}

.team-section .team-member:hover {
    transform: translateY(-10px);
}

.team-section .team-member-img {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    margin: 0 auto 20px;
    border: 4px solid rgba(255, 255, 255, 0.3);
}

.team-section .team-member h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.team-section .team-member-role {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    margin-bottom: 15px;
}

/* Chart Section Specific Styles */
.chart-section .chart-container {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    margin-top: 50px;
}

.chart-section .chart-entry {
    display: flex;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e2e8f0;
    transition: background-color 0.3s ease;
}

.chart-section .chart-entry:hover {
    background-color: #f8fafc;
}

.chart-section .chart-entry:last-child {
    border-bottom: none;
}

.chart-section .chart-position {
    font-size: 2rem;
    font-weight: 700;
    color: #8B5CF6;
    margin-right: 20px;
    min-width: 60px;
}

.chart-section .chart-song-info {
    flex: 1;
}

.chart-section .chart-song-title {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 5px;
}

.chart-section .chart-artist {
    color: #64748b;
    font-size: 14px;
}

.chart-section .chart-album-art {
    width: 60px;
    height: 60px;
    border-radius: 10px;
    object-fit: cover;
    margin-left: 20px;
}

/* Top 3 Section Specific Styles */
.top3-section .top3-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.top3-section .top3-card {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    height: 300px;
}

.top3-section .top3-card-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-size: cover;
    background-position: center;
}

.top3-section .top3-card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(139, 92, 246, 0.8), rgba(124, 58, 237, 0.8));
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 30px;
}

.top3-section .top3-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 15px;
}

/* Featured Videos Section Specific Styles */
.videos-section .video-player {
    background: #000;
    border-radius: 20px;
    overflow: hidden;
    margin: 30px 0;
    aspect-ratio: 16/9;
}

/* Prayer Section Specific Styles */
.prayer-section .prayer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.prayer-section .prayer-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 30px;
    backdrop-filter: blur(10px);
}

.prayer-section .prayer-card h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 15px;
}

/* Newsletter Section Specific Styles */
.newsletter-section .newsletter-form {
    max-width: 500px;
    margin: 30px auto 0;
    display: flex;
    gap: 15px;
}

.newsletter-section .newsletter-input {
    flex: 1;
    padding: 15px 20px;
    border: none;
    border-radius: 25px;
    font-size: 16px;
}

.newsletter-section .newsletter-input:focus {
    outline: none;
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
}
