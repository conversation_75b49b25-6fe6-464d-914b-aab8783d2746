/* ==========================================================================
   LAYOUT STYLES
   ========================================================================== */

/* Grid Layouts */
.news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 50px;
    position: relative;
    z-index: 2;
}

.top3-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.prayer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.renungan-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 60px;
}

/* Section Layouts */
.next-event-section {
    padding: 60px 0;
    background: #f8fafc;
}

.latest-news-section {
    padding: 80px 0;
    background: white;
}

.team-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    color: white;
    position: relative;
}

.team-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
}

.chart-section {
    padding: 80px 0;
    background: #f8fafc;
}

.top3-section {
    padding: 80px 0;
    background: #1e293b;
    color: white;
}

.videos-section {
    padding: 80px 0;
    background: white;
}

.prayer-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #C084FC, #A855F7);
    color: white;
}

.newsletter-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #1e293b, #334155);
    color: white;
    text-align: center;
}

/* Archive Layouts */
.renungan-archive {
    background: #f8fafc;
    min-height: 100vh;
    padding: 40px 0;
}

.archive-header {
    text-align: center;
    margin-bottom: 60px;
}

.archive-hero {
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    color: white;
    padding: 60px 40px;
    border-radius: 20px;
    position: relative;
    overflow: hidden;
}

.archive-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
}

.archive-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 20px;
    position: relative;
    z-index: 2;
    font-family: 'Poppins', sans-serif;
}

.archive-description {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
    line-height: 1.6;
}

/* Filter Layouts */
.renungan-filters {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
    flex-wrap: wrap;
    gap: 20px;
}

.filter-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.search-box {
    display: flex;
    align-items: center;
}

.search-box form {
    display: flex;
    align-items: center;
    background: white;
    border-radius: 25px;
    padding: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Navigation Layouts */
.nav-links {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.nav-next {
    text-align: right;
}

/* CTA Layouts */
.renungan-cta {
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    color: white;
    padding: 60px 40px;
    border-radius: 20px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.renungan-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
}

.cta-content {
    position: relative;
    z-index: 2;
}

/* Pagination Layout */
.pagination {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin: 3rem 0;
}

.renungan-pagination {
    display: flex;
    justify-content: center;
    margin-bottom: 60px;
}

/* Share Buttons Layout */
.share-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

/* No Posts Layout */
.no-posts {
    grid-column: 1 / -1;
    text-align: center;
    padding: 60px 20px;
}

.no-posts-content {
    max-width: 400px;
    margin: 0 auto;
}
