/**
 * Contact Base JavaScript
 * ElshadaiFM Theme - Contact Page
 */

(function($) {
    'use strict';

    // Contact Base Module
    window.ContactBase = {
        
        // Configuration
        config: {
            animationDuration: 300,
            scrollOffset: 100,
            debounceDelay: 250
        },

        // Initialize base functionality
        init: function() {
            this.bindEvents();
            this.initScrollAnimations();
            this.initAccessibility();
            console.log('Contact Base initialized');
        },

        // Bind base events
        bindEvents: function() {
            $(window).on('scroll', this.debounce(this.handleScroll.bind(this), this.config.debounceDelay));
            $(window).on('resize', this.debounce(this.handleResize.bind(this), this.config.debounceDelay));
            
            // Smooth scrolling for anchor links
            $('a[href^="#"]').on('click', this.handleAnchorClick.bind(this));
        },

        // Initialize scroll animations
        initScrollAnimations: function() {
            const sections = $('.contact-info-section, .map-section, .faq-section, .cta-section');
            
            sections.each(function() {
                const $section = $(this);
                $section.css({
                    'opacity': '0',
                    'transform': 'translateY(50px)'
                });
            });

            this.handleScroll();
        },

        // Handle scroll events
        handleScroll: function() {
            const scrollTop = $(window).scrollTop();
            const windowHeight = $(window).height();

            // Animate sections on scroll
            $('.contact-info-section, .map-section, .faq-section, .cta-section').each(function() {
                const $section = $(this);
                const sectionTop = $section.offset().top;
                
                if (scrollTop + windowHeight > sectionTop + 100) {
                    $section.css({
                        'opacity': '1',
                        'transform': 'translateY(0)',
                        'transition': 'all 0.6s ease-out'
                    });
                }
            });

            // Animate FAQ items
            $('.faq-item').each(function(index) {
                const $item = $(this);
                const itemTop = $item.offset().top;
                
                if (scrollTop + windowHeight > itemTop + 50) {
                    setTimeout(() => {
                        $item.css({
                            'opacity': '1',
                            'transform': 'translateY(0)',
                            'transition': 'all 0.5s ease-out'
                        });
                    }, index * 100);
                }
            });

            // Animate contact info items
            $('.contact-info-item').each(function(index) {
                const $item = $(this);
                const itemTop = $item.offset().top;
                
                if (scrollTop + windowHeight > itemTop + 50) {
                    setTimeout(() => {
                        $item.css({
                            'opacity': '1',
                            'transform': 'translateX(0)',
                            'transition': 'all 0.4s ease-out'
                        });
                    }, index * 150);
                }
            });
        },

        // Handle window resize
        handleResize: function() {
            // Recalculate positions and animations
            this.handleScroll();
        },

        // Handle anchor link clicks
        handleAnchorClick: function(e) {
            const href = $(e.currentTarget).attr('href');
            
            if (href.startsWith('#') && href.length > 1) {
                const target = $(href);
                
                if (target.length) {
                    e.preventDefault();
                    
                    $('html, body').animate({
                        scrollTop: target.offset().top - this.config.scrollOffset
                    }, this.config.animationDuration);
                }
            }
        },

        // Initialize accessibility features
        initAccessibility: function() {
            // Add ARIA labels to interactive elements
            $('.social-link').each(function() {
                const $link = $(this);
                const platform = $link.attr('class').split(' ').find(cls => 
                    ['facebook', 'youtube', 'instagram', 'tiktok'].includes(cls)
                );
                
                if (platform) {
                    $link.attr('aria-label', `Follow us on ${platform.charAt(0).toUpperCase() + platform.slice(1)}`);
                }
            });

            // Add focus management
            $('.faq-item, .contact-info-item').attr('tabindex', '0');
            
            // Handle keyboard navigation
            $('.faq-item, .contact-info-item').on('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    $(this).trigger('click');
                }
            });
        },

        // Utility: Debounce function
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        // Utility: Check if element is in viewport
        isInViewport: function(element) {
            const rect = element.getBoundingClientRect();
            return (
                rect.top >= 0 &&
                rect.left >= 0 &&
                rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                rect.right <= (window.innerWidth || document.documentElement.clientWidth)
            );
        },

        // Utility: Animate element
        animateElement: function($element, animation, delay = 0) {
            setTimeout(() => {
                $element.addClass(animation);
            }, delay);
        },

        // Show loading state
        showLoading: function($element) {
            $element.addClass('loading').prop('disabled', true);
        },

        // Hide loading state
        hideLoading: function($element) {
            $element.removeClass('loading').prop('disabled', false);
        },

        // Show message
        showMessage: function(message, type = 'info', duration = 5000) {
            const $message = $(`
                <div class="contact-message contact-message--${type}">
                    <span class="contact-message__text">${message}</span>
                    <button class="contact-message__close" aria-label="Close message">×</button>
                </div>
            `);

            $('body').append($message);

            // Animate in
            setTimeout(() => {
                $message.addClass('contact-message--visible');
            }, 100);

            // Auto hide
            if (duration > 0) {
                setTimeout(() => {
                    this.hideMessage($message);
                }, duration);
            }

            // Close button
            $message.find('.contact-message__close').on('click', () => {
                this.hideMessage($message);
            });

            return $message;
        },

        // Hide message
        hideMessage: function($message) {
            $message.removeClass('contact-message--visible');
            setTimeout(() => {
                $message.remove();
            }, 300);
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        ContactBase.init();
    });

})(jQuery);
